@file:Suppress("UnstableApiUsage")

import com.google.protobuf.gradle.id
import org.jlleitschuh.gradle.ktlint.reporter.ReporterType
import java.io.ByteArrayOutputStream
import java.io.FileInputStream
import java.io.InputStreamReader
import java.util.Properties

plugins {
    alias(libs.plugins.firebase.crashlytics) apply false
    alias(libs.plugins.google.services)
    alias(libs.plugins.android.kotlin)
    alias(libs.plugins.android.hilt)
    alias(libs.plugins.ksp)
    id("kotlin-parcelize")
    id("kotlin-android")
    id("androidx.navigation.safeargs.kotlin")
    id("com.google.firebase.firebase-perf")
    id("com.google.protobuf")
    id("org.jlleitschuh.gradle.ktlint")
    id("oneapp.android.application.flavors")
}

android {
    namespace = "com.toyota.oneapp"

    compileSdk = rootProject.extra.get("compileSdkVersion") as Int

    defaultConfig {
        applicationId = rootProject.extra.get("appId") as String
        minSdk = rootProject.extra.get("minSdkVersion") as Int
        targetSdk = rootProject.extra.get("targetSdkVersion") as Int
        testInstrumentationRunner = "com.toyota.oneapp.CustomAndroidJUnitRunner"
        vectorDrawables.useSupportLibrary = true

        wearAppUnbundled = true

        manifestPlaceholders["appAuthRedirectScheme"] = "oneapp://redirect"
        manifestPlaceholders["branchTestMode"] = true

        ndk {
            // Filter for architectures supported by Flutter.
            abiFilters.add("armeabi-v7a")
            abiFilters.add("arm64-v8a")
            abiFilters.add("x86_64")
        }

        buildConfigField("boolean", "UI_REFRESH_LANDING_ENABLED", "false")
        buildConfigField("boolean", "CY17PLUS_ACH_PAYMENT_ENABLED", "false") // cleanup tests
        buildConfigField("boolean", "SECURITY_SETTINGS_ENABLED", "false")
        buildConfigField("boolean", "MAKETING_CARDS_LCFS_ENABLED", "true")
        buildConfigField("boolean", "MEXICO_REGION_ENABLED", "true")
        buildConfigField("boolean", "REMOTE_DTC_ENABLED", "false")
        buildConfigField("boolean", "SUBSCRIPTION_PURCHASE_MULTI_PRODUCT_ENABLED", "true")
        buildConfigField("boolean", "DIGITALKEY_SHARE", "true")
        buildConfigField("boolean", "FEATURE_FLAG_TOGGLE", "false")
        buildConfigField("boolean", "DK_PULL_MECHANISM_ENABLED", "false")
        buildConfigField("boolean", "CHARGE_ASSIST_FEATURE_ENABLED", "true")

        // flutter depreciation config flags
        buildConfigField("boolean", "GUEST_DRIVER_FLUTTER_DEPRECIATION", "true")
        buildConfigField("boolean", "ANNOUNCEMENT_CENTER_FLUTTER_DEPRECIATION", "true")
        buildConfigField("boolean", "DEALER_SERVICE_FLUTTER_DEPRECATION", "false")
        buildConfigField("boolean", "FIND_STATIONS_FLUTTER_DEPRECIATION", "true")
        buildConfigField("boolean", "CHARGE_STATIONS_FLUTTER_DEPRECIATION", "true")
        buildConfigField("boolean", "CHARGE_INFO_FLUTTER_DEPRECATION", "true")
        buildConfigField("boolean", "WALLET_HOME_FLUTTER_DEPRECATION", "true")
        buildConfigField("boolean", "DRIVE_PULSE_AND_TRIPS_FLUTTER_DEPRECATION", "false")
        buildConfigField("boolean", "SHOW_IONNA_CARD", "true")
        buildConfigField("boolean", "SHOW_TESLA_CARD", "true")
        buildConfigField("boolean", "SHOW_PLUG_AND_CHARGE_CARD", "true")
    }

    lint {
        checkReleaseBuilds = false
        abortOnError = false
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    buildFeatures {
        buildConfig = true
        dataBinding = true
        viewBinding = true
        compose = true
    }

    composeOptions {
        kotlinCompilerExtensionVersion = "1.5.15"
    }

    packaging {
        resources {
            excludes.add("lib/font/sf_pro_text_regular.otf")
            excludes.add("lib/font/sf_pro_display_regular.otf")
            excludes.add("lib/font/sf_pro_text_semibold.otf")
            excludes.add("lib/font/sf_pro_text_bold.otf")
        }
        jniLibs {
            excludes.add("**/libvulkan.so")
            excludes.add("**/libVkLayer_khronos_validation.so")
        }
        resources.excludes.add("META-INF/DEPENDENCIES")
        resources.pickFirsts.add("lib/arm64-v8a/libc++_shared.so")
    }

    signingConfigs {
        getByName("debug") {
            keyAlias = "debug keystore"
            keyPassword = "098765"
            storeFile = file("../storeFiles/debug.keystore")
            storePassword = "098765toyota"
        }
        create("naLexusKey") {
            keyAlias = System.getenv("LEXUS_KEY_ALIAS")
            keyPassword = System.getenv("LEXUS_KEY_PASSWORD")
            storeFile = file("../storeFiles/na/lexus_release.keystore")
            storePassword = System.getenv("LEXUS_STORE_PASSWORD")
        }
        create("naToyotaKey") {
            keyAlias = System.getenv("TOYOTA_KEY_ALIAS")
            keyPassword = System.getenv("TOYOTA_KEY_PASSWORD")
            storeFile = file("../storeFiles/na/toyota_release.keystore")
            storePassword = System.getenv("TOYOTA_STORE_PASSWORD")
        }
        create("naSubaruKey") {
            keyAlias = System.getenv("SUBARU_KEY_ALIAS")
            keyPassword = System.getenv("SUBARU_KEY_PASSWORD")
            storeFile = file("../storeFiles/na/subaru_release.keystore")
            storePassword = System.getenv("SUBARU_STORE_PASSWORD")
        }
        create("auLexusKey") {
            keyAlias = System.getenv("AU_LEXUS_KEY_ALIAS")
            keyPassword = System.getenv("AU_LEXUS_KEY_PASSWORD")
            storeFile = file("../storeFiles/au/lexus_release.keystore")
            storePassword = System.getenv("AU_LEXUS_STORE_PASSWORD")
        }
        create("auToyotaKey") {
            keyAlias = System.getenv("AU_TOYOTA_KEY_ALIAS")
            keyPassword = System.getenv("AU_TOYOTA_KEY_PASSWORD")
            storeFile = file("../storeFiles/au/toyota_release.keystore")
            storePassword = System.getenv("AU_TOYOTA_STORE_PASSWORD")
        }
    }
    bundle {
        language {
            enableSplit = false
        }
    }

    buildTypes {
        getByName("debug") {
            val enableMinification =
                System.getenv("CI_MERGE_REQUEST_TARGET_BRANCH_NAME") != null
            println("enableMinification :$enableMinification")

            // minify at GitLab only not yet developer machine.
            if (enableMinification) {
                isMinifyEnabled = true
                isShrinkResources = true
            }
            proguardFiles(getDefaultProguardFile("proguard-android.txt"), "proguard-rules.pro", "gson.pro")
        }
        create("profile") {
            initWith(getByName("debug"))
        }
        getByName("release") {
            multiDexKeepProguard = file("multidex-config.pro")
            isMinifyEnabled = true
            isShrinkResources = true
            proguardFiles(getDefaultProguardFile("proguard-android.txt"), "proguard-rules.pro", "gson.pro")

            productFlavors
                .getByName("naToyota")
                .signingConfig =
                signingConfigs.getByName(
                    "naToyotaKey",
                )
            productFlavors.getByName("naLexus").signingConfig =
                signingConfigs.getByName(
                    "naLexusKey",
                )
            productFlavors
                .getByName("naSubaru")
                .signingConfig = signingConfigs.getByName("naSubaruKey")

            productFlavors
                .getByName("auToyota")
                .signingConfig = signingConfigs.getByName("auToyotaKey")

            productFlavors
                .getByName("auLexus")
                .signingConfig = signingConfigs.getByName("auLexusKey")
            kotlinOptions {
                freeCompilerArgs += "-opt-in=kotlin.RequiresOptIn"
            }
        }
    }

    applicationVariants.configureEach {
        val variant = this
        val mergedFlavor = variant.mergedFlavor
        // Manifest placeholders don"t merge very well when there are multiple dimensions so
        // We must replace some values here for prod branch deep linking based on brand
        if (variant.name.lowercase().contains("prod")) {
            mergedFlavor.manifestPlaceholders.replace("branchTestMode", false)
            if (variant.name.contains("lexus")) {
                mergedFlavor.manifestPlaceholders.replace("branchHostName", "ctlexusapp.com")
            } else if (variant.name.contains("toyota")) {
                mergedFlavor.manifestPlaceholders.replace("branchHostName", "cttoyotaapp.com")
            } else if (variant.name.contains("subaru")) {
                mergedFlavor.manifestPlaceholders.replace("branchHostName", "subaru.app.link")
            }
        }
    }

    testOptions {
        unitTests {
            isIncludeAndroidResources = true
            isReturnDefaultValues = true
        }
    }

    subprojects {
        beforeEvaluate {
            if (name == "app_settings" || name == "location" || name == "share_plus") {
                buildscript.dependencies.add(
                    "classpath",
                    "org.jetbrains.kotlin:kotlin-gradle-plugin:1.8.22",
                )
            }
        }
    }
}

kotlin {
    jvmToolchain(17)
}

fun getPropertyValue(key: String): String? {
    val properties = Properties()
    val localProperties = File("gradle.properties")
    if (localProperties.isFile) {
        InputStreamReader(FileInputStream(localProperties), Charsets.UTF_8).use { reader ->
            properties.load(reader)
        }
    } else {
        error("File from not found")
    }

    return properties.getProperty(key)
}

fun getProps(path: String): Properties {
    val props = Properties()
    props.load(FileInputStream(file(path)))
    return props
}

protobuf {
    protoc { artifact = "com.google.protobuf:protoc:${rootProject.extra.get("protobufVersion")}" }
    plugins {
        id("grpc") {
            artifact = "io.grpc:protoc-gen-grpc-java:${rootProject.extra.get("grpcVersion")}"
        }
    }
    generateProtoTasks {
        all().forEach { task ->
            task.builtins {
                create("java") {
                    option("lite")
                }
            }
            task.plugins {
                create("grpc") {
                    option("lite")
                }
            }
        }
    }
}

repositories {
    flatDir {
        dirs("../libs")
        dirs("../repo")
    }
}

repositories {
    maven {
        url = uri("../repo/flutterBuild/host/outputs/repo")
        // This is relative to the location of the build.gradle file
        // if using a relative path.
    }
    maven {
        url = uri("https://storage.googleapis.com/download.flutter.io")
    }
}

configure<org.jlleitschuh.gradle.ktlint.KtlintExtension> {
    debug.set(true)
    verbose.set(true)
    android.set(true)
    outputToConsole.set(true)
    outputColorName.set("RED")
    baseline.set(File("$rootDir/code-quality/ktlint/baseline.xml"))
    reporters {
        reporter(ReporterType.PLAIN)
        reporter(ReporterType.CHECKSTYLE)
        reporter(ReporterType.HTML)
    }
}

tasks.register("formatChangedFiles") {

    doLast {
        // Run Git command to find changed files
        val changedFiles =
            ByteArrayOutputStream().use { outputStream ->
                exec {
                    commandLine("git", "diff", "--name-only", "--diff-filter=AMR", "HEAD")
                    standardOutput = outputStream
                }
                outputStream
                    .toString()
                    .split("\n")
                    .filter { it.endsWith(".kt") }
                    .map { file(it) }
            }

        // Check if there are any changed Kotlin files
        if (changedFiles.isEmpty()) {
            println("No Kotlin files changed.")
        } else {
            println("Formatting changed files: $changedFiles")
            // Run ktlintFormat on changed files
            changedFiles.forEach { file ->
                exec {
                    commandLine("gradlew", "ktlintFormat", "--files", file)
                }
            }
        }
    }
}

dependencies {
    implementation(fileTree(mapOf("dir" to "libs", "include" to listOf("*.jar"))))

    api(project(":network"))
    implementation(libs.androidx.legacy.support.v4)
    components.all {
        allVariants {
            withDependencies {
                val deps = this
                deps.forEach { dep ->
                    if (dep.group == "net.minidev" && dep.name == "json-smart") {
                        dep.version {
                            prefer("2.3")
                        }
                        dep.because("resolving dependencies issue")
                    }
                }
            }
        }
    }

    api(project(":commonlib"))
    api(project(":xcapp"))
    api(project(":one_ui"))
    api(project(":forgerock-auth-ui"))
    implementation(project(":flutter")) {
        exclude(module = "exoplayer-core")
        exclude(module = "exoplayer-hls")
        exclude(module = "exoplayer-dash")
        exclude(module = "exoplayer-smoothstreaming")
    }

    api(libs.androidx.activity.ktx)
    implementation(libs.androidx.ads.identifier)
    api(libs.androidx.appcompat)
    implementation(libs.androidx.core.runtime)
    implementation(libs.androidx.biometric)
    implementation(libs.androidx.browser)
    implementation(libs.androidx.cardview)
    implementation(libs.androidx.constraintlayout)
    api(libs.androidx.core.ktx)
    api(libs.androidx.fragment.ktx)
    implementation(libs.androidx.legacy.support.core.ui)
    api(libs.androidx.lifecycle.compiler)
    api(libs.androidx.lifecycle.livedata.ktx)
    api(libs.androidx.lifecycle.viewmodel.ktx)
    api(libs.androidx.multidex)
    implementation(libs.androidx.lifecycle.livedata.ktx)
    implementation(libs.androidx.navigation.fragment.ktx)
    implementation(libs.androidx.navigation.ui.ktx)
    // compose navigator
    implementation(libs.androidx.navigation.navigation.compose)
    implementation(libs.compose.material.icon)

    implementation(libs.androidx.recyclerview)
    implementation(libs.androidx.recyclerview.recyclerview.selection)
    api(libs.work.rxjava2)
    implementation(libs.work.runtime)
    implementation(libs.work.runtime.ktx)
    implementation(libs.fabulousfilter)
    api(libs.jwtdecode)
    implementation(libs.android.state)
    implementation(libs.glide)
    implementation(libs.persistentCookieJar)
    implementation(libs.magicIndicator)
    implementation(libs.mpAndroidChart)
    implementation(libs.play.services.location)
    implementation(libs.play.services.maps)
    implementation(libs.play.services.wearable)
    implementation(libs.gms.play.services.basement)
    api(libs.play.feature.delivery)
    api(libs.play.feature.delivery.ktx)
    api(libs.play.review)
    api(libs.play.review.ktx)
    api(libs.material)
    api(libs.androidx.compose.material3)
    api(libs.gson)
    api(libs.firebase.core)
    api(libs.firebase.messaging)
    implementation(libs.firebase.analytics.ktx) {
        exclude(group = "com.google.api.grpc", module = "proto-google-common-protos")
    }
    implementation(libs.guava)
    implementation(libs.android.maps.utils)
    api(libs.rxbinding)
    implementation(libs.roundedimageview)
    implementation(libs.androidyoutubeplayer.core)
    implementation(libs.logging.interceptor)
    implementation(libs.okhttp)
    implementation(libs.okhttp.urlconnection)
    implementation(libs.adapter.rxjava2)
    implementation(libs.converter.gson)
    implementation(libs.retrofit)
    implementation(libs.recyclerview.swipe)
    implementation(libs.hdodenhof.circleimageview)
    api(libs.rxandroid)
    api(libs.rxjava)
    implementation(libs.multitype)
    implementation(libs.appauth)
    implementation(libs.commons.lang3)
    implementation(libs.anko.commons)
    implementation(libs.commons.base)
    implementation(libs.kotlin.reflect)
    implementation(libs.android.reactive.location2)
    implementation(libs.android.gif.drawable)
    implementation(libs.library)
    implementation(libs.fadingedgelayout)
    implementation(libs.basePopup)

    // Google map for compose
    implementation(libs.maps.compose)

    // jsoup for html parsing
    implementation(libs.jsoup)

    // gRPC
    api(libs.grpc.okhttp)
    api(libs.grpc.protobuf)
    api(libs.grpc.stub)
    implementation(libs.junit)
    implementation(libs.firebase.firestore.ktx) {
        exclude(group = "com.google.api.grpc", module = "proto-google-common-protos")
    }
    testApi(libs.grpc.testing)
    implementation(libs.protobuf.java)
    implementation(libs.javax.annotation.api)

    implementation(libs.places)
    implementation(libs.fadingedgelayout)

//    ksp(libs.android.state)
//    ksp(libs.glide.ksp)
//    ksp(libs.kotlinx.metadata.jvm)

    // Android Beacon Library - For Bluetooth auto-launch
    api(libs.android.beacon.library)

    // Google ML for VIN Scan and QR Scan Feature
    implementation(libs.play.services.mlkit.barcode.scanning)
    implementation(libs.play.services.mlkit.text.recognition)
    // CameraX library
    implementation(libs.androidx.camera.camera2)
    implementation(libs.androidx.camera.lifecycle)
    implementation(libs.androidx.camera.view)

    // new arch libs start --->
    implementation(libs.compose.runtime)
    implementation(libs.compose.ui)
    implementation(libs.compose.foundation)
    implementation(libs.compose.foundation.layout)
    implementation(libs.compose.material)
    implementation(libs.compose.runtime.livedata)
    implementation(libs.compose.ui.tooling)
// When using a AppCompat theme
    implementation(libs.compose.ui.util)
    // Todo Need change with compose lib
    implementation(libs.accompanist.appcompat.theme)
    implementation(libs.accompanist.flowlayout)
    implementation(libs.accompanist.pager)
    implementation(libs.accompanist.pager.indicators)

    // coroutines
    implementation(libs.kotlinx.coroutines.core)
    implementation(libs.kotlinx.coroutines.android)
    implementation(libs.glide.compose)
    implementation(libs.androidx.ui.tooling.preview)
    implementation(libs.coil.compose)

    // Hilt dependencies
    implementation(libs.hilt.android)
    ksp(libs.hilt.compiler)
    implementation(libs.androidx.hilt.navigation.compose)
//    ksp(libs.androidx.lifecycle.viewmodel.compose)
    // <--- new arch libs end

    // Secured shared preference
    implementation(libs.androidx.security.crypto)

    implementation(platform(libs.firebase.bom))

    // Add the dependencies for the Crashlytics and Analytics libraries
    // When using the BoM, you don't specify versions in Firebase library dependencies
    implementation(libs.firebase.crashlytics)

    // Digitalkey Libs
    implementation(libs.timber)
    implementation(libs.eclipse.source) {
        artifact {
            type = "aar"
        }
    }
    debugImplementation(libs.da.secureDebugCheckOffSafetyCheckOffEmulatorCheckOnLogs.release)
    debugImplementation(group = "", name = "dkctllib_debug", ext = "aar")
    debugImplementation(group = "", name = "dklib_debug", ext = "aar")
    releaseImplementation(libs.da.secureDebugCheckOnSafetyCheckOffEmulatorCheckOnNologs.release)
    releaseImplementation(group = "", name = "dkctllib_release", ext = "aar")
    releaseImplementation(group = "", name = "dklib_release", ext = "aar")
    implementation(libs.datadog.android)
    // RemoteParkControlLib
    implementation(project(":RemoteParkControlLib"))

    implementation(libs.androidx.lifecycle.runtime.ktx)
    implementation(libs.androidx.lifecycle.viewmodel.savedstate)
    implementation(libs.androidx.navigation.dynamic.features.fragment)

    implementation(libs.apptentive) {
        exclude(group = "com.google.android.play", module = "core-common")
        exclude(group = "com.google.android.play", module = "review-ktx")
        exclude(group = "com.google.android.play", module = "review")
    }

    // Firebase performance monitoring
    implementation(libs.firebase.perf) {
        exclude(group = "com.google.firebase", module = "protolite-well-known-types")
        exclude(group = "com.google.protobuf", module = "protobuf-javalite")
    }

    // Video player
    implementation(libs.androidx.media3.exoplayer)
    implementation(libs.androidx.media3.ui)

    // Stripe
    implementation(libs.stirpe.android) {
        exclude(group = "org.bouncycastle", module = "bcprov-jdk15to18")
    }

    // SystemUI Controller
    implementation(libs.accompanist.systemuicontroller)

    // debug impl
    debugImplementation(libs.compose.ui.tooling)
    debugImplementation(libs.androidx.ui.test.manifest) // revisit

    // kapt android test
//    kspAndroidTest(libs.hilt.android.compiler)

    // test rest api
    testImplementation(libs.okhttp3.mockwebserver)

    // test impl
    testImplementation(libs.json.json)
    testImplementation(libs.androidx.test.core)
    testImplementation(libs.core.testing)
    testImplementation(libs.junit) // revisit
    testImplementation(libs.kotlinx.coroutines.test) {
        // https://github.com/Kotlin/kotlinx.coroutines/tree/master/kotlinx-coroutines-debug#debug-agent-and-android
        exclude(group = "org.jetbrains.kotlinx", module = "kotlinx-coroutines-debug")
    }
    testImplementation(libs.mockito.core)
    testImplementation(libs.mockito.inline)
    testImplementation(libs.mockito.kotlin)
    testImplementation(libs.powermock.api.mockito2)
    testImplementation(libs.powermock.core)
    testImplementation(libs.powermock.module.junit4)
    testImplementation(libs.mockk)

    // android test
    androidTestImplementation(libs.androidx.rules)
    androidTestImplementation(libs.androidx.runner)
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.ui.test.junit4)
    androidTestImplementation(libs.hilt.android.testing)
    androidTestImplementation(libs.mockk.android)
    androidTestImplementation(libs.androidx.uiautomator)
}

sourceSets.create("main").java {
    srcDirs("build/generated/source/proto/main/java")
}

configurations {
    all {
        exclude("com.google.android.play", "core")
        exclude(group = "com.google.firebase", module = "protolite-well-known-types")
        exclude(group = "com.google.protobuf", module = "protobuf-javalite")
        resolutionStrategy {
            eachDependency {
                if ((requested.group == "org.jetbrains.kotlin") && (requested.name.startsWith("kotlin-stdlib"))) {
                    useVersion("1.9.23")
                }
            }
            force("com.google.protobuf:protobuf-java:3.22.3")
        }
    }

    configureEach {
        resolutionStrategy {
            force(libs.play.services.location)
            force(libs.androidx.camera.camera2)
            force(libs.androidx.camera.lifecycle)
            force(libs.androidx.camera.view)
        }
    }
}

tasks.withType<Test> {
    jvmArgs("--add-opens", "java.base/java.lang=ALL-UNNAMED")
    jvmArgs("--add-opens", "java.base/java.text=ALL-UNNAMED")
    jvmArgs("--add-opens", "java.base/java.time=ALL-UNNAMED")
    jvmArgs("--add-opens", "java.base/java.io=ALL-UNNAMED")
    jvmArgs("--add-opens", "java.base/java.util=ALL-UNNAMED")
    jvmArgs("--add-opens", "java.base/java.lang.reflect=ALL-UNNAMED")
}
