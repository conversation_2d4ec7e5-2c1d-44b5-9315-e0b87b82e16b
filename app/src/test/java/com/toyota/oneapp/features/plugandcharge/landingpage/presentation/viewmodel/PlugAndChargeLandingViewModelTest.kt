package com.toyota.oneapp.features.plugandcharge.landingpage.presentation.viewmodel

import com.nhaarman.mockitokotlin2.any
import com.nhaarman.mockitokotlin2.whenever
import com.toyota.oneapp.features.chargingnetwork.domain.model.ChargingNetworkType
import com.toyota.oneapp.features.chargingnetwork.domain.model.PlugAndChargeLandingState
import com.toyota.oneapp.features.plugandcharge.landingpage.domain.model.PlugAndChargeToggleStatus
import com.toyota.oneapp.features.plugandcharge.landingpage.domain.usecase.GetPlugAndChargeCompatibleChargingNetworksUseCase
import com.toyota.oneapp.features.plugandcharge.landingpage.domain.usecase.GetPlugAndChargeToggleStatusUseCase
import com.toyota.oneapp.features.plugandcharge.landingpage.domain.usecase.SetPlugAndChargeToggleStatusUseCase
import com.toyota.oneapp.util.DateUtil
import junit.framework.TestCase.assertEquals
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.advanceUntilIdle
import kotlinx.coroutines.test.resetMain
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.test.setMain
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.junit.MockitoJUnitRunner

@OptIn(ExperimentalCoroutinesApi::class)
@RunWith(MockitoJUnitRunner.Silent::class)
class PlugAndChargeLandingViewModelTest {
    private val testDispatcher = StandardTestDispatcher()

    private lateinit var viewModel: PlugAndChargeLandingViewModel

    @Mock
    lateinit var fakeDateUtil: DateUtil

    private class FakeGetPlugAndChargeToggleStatusUseCase(
        private val initial: PlugAndChargeToggleStatus,
    ) : GetPlugAndChargeToggleStatusUseCase {
        override suspend fun invoke(): StateFlow<Result<PlugAndChargeToggleStatus>> = MutableStateFlow(Result.success(initial))
    }

    private class FakeSetPlugAndChargeToggleStatusUseCase : SetPlugAndChargeToggleStatusUseCase {
        override suspend fun invoke(newValue: Boolean): Result<Unit> = Result.success(Unit)
    }

    private class FakeGetPlugAndChargeCompatibleChargingNetworksUseCase(
        initial: List<ChargingNetworkType> = emptyList(),
    ) : GetPlugAndChargeCompatibleChargingNetworksUseCase {
        private val networks = MutableStateFlow(initial)

        override fun invoke(): Flow<List<ChargingNetworkType>> = networks.asStateFlow()
    }

    @Before
    fun setup() {
        Dispatchers.setMain(testDispatcher)
        whenever(fakeDateUtil.formatMediumDate(any())).thenReturn("20/05/2025")
    }

    @After
    fun tearDown() {
        Dispatchers.resetMain()
    }

    private fun prepareScenario() {
        val fakeGetPlugAndChargeToggleStatusUseCase =
            FakeGetPlugAndChargeToggleStatusUseCase(
                PlugAndChargeToggleStatus(
                    isToggleOn = false,
                    expirationDate = null,
                ),
            )
        val fakeSetPlugAndChargeToggleStatusUseCase = FakeSetPlugAndChargeToggleStatusUseCase()
        val fakeGetPlugAndChargeCompatibleChargingNetworksUseCase =
            FakeGetPlugAndChargeCompatibleChargingNetworksUseCase(
                listOf(ChargingNetworkType.TESLA),
            )

        viewModel =
            PlugAndChargeLandingViewModel(
                fakeGetPlugAndChargeToggleStatusUseCase,
                fakeSetPlugAndChargeToggleStatusUseCase,
                fakeGetPlugAndChargeCompatibleChargingNetworksUseCase,
                dateUtil = fakeDateUtil,
            )
    }

    @Test
    fun `on init`() =
        runTest {
            val expectedEnabled = false
            val expectedNetworks = listOf(ChargingNetworkType.TESLA)

            prepareScenario()
            advanceUntilIdle()

            val loadedState = viewModel.plugAndChargeState.value as PlugAndChargeLandingState.Loaded
            assertEquals(expectedEnabled, loadedState.isEnabled)

            val compatibleStations = viewModel.compatibleStationsState.value
            assertEquals(expectedNetworks, compatibleStations)
        }

    @Test
    fun `onPlugAndChargeStatusChange updates use case`() =
        runTest {
            prepareScenario()
            viewModel.onPlugAndChargeStatusChange(true)
        }
}
