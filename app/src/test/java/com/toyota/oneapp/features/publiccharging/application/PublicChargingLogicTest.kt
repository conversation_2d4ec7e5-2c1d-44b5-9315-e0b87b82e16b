/*
 * Copyright © 2024. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.publiccharging.application

import com.nhaarman.mockitokotlin2.anyOrNull
import com.nhaarman.mockitokotlin2.argumentCaptor
import com.nhaarman.mockitokotlin2.eq
import com.nhaarman.mockitokotlin2.verify
import com.nhaarman.mockitokotlin2.whenever
import com.toyota.ctp.v1.ProfileServiceServer
import com.toyota.ctp.v1.ProfileServiceTier1
import com.toyota.oneapp.R
import com.toyota.oneapp.features.findstations.dataaccess.servermodel.Connectors
import com.toyota.oneapp.features.findstations.dataaccess.servermodel.Elements
import com.toyota.oneapp.features.findstations.dataaccess.servermodel.Evses
import com.toyota.oneapp.features.findstations.dataaccess.servermodel.Geometry
import com.toyota.oneapp.features.findstations.dataaccess.servermodel.OpeningTimes
import com.toyota.oneapp.features.findstations.dataaccess.servermodel.PartnerInfo
import com.toyota.oneapp.features.findstations.dataaccess.servermodel.PriceComponents
import com.toyota.oneapp.features.findstations.dataaccess.servermodel.Station
import com.toyota.oneapp.features.findstations.dataaccess.servermodel.StationInfo
import com.toyota.oneapp.features.findstations.dataaccess.servermodel.StationsListPayload
import com.toyota.oneapp.features.findstations.dataaccess.servermodel.StationsListResponse
import com.toyota.oneapp.features.findstations.dataaccess.servermodel.TariffAltText
import com.toyota.oneapp.features.findstations.dataaccess.servermodel.TariffInfo
import com.toyota.oneapp.features.publiccharging.domain.model.Messages
import com.toyota.oneapp.features.publiccharging.domain.model.StartChargeResponse
import com.toyota.oneapp.features.publiccharging.domain.model.StartChargingData
import com.toyota.oneapp.features.publiccharging.domain.model.StartChargingPayload
import com.toyota.oneapp.features.publiccharging.domain.model.StartChargingRequest
import com.toyota.oneapp.features.publiccharging.domain.model.StopChargingData
import com.toyota.oneapp.features.publiccharging.domain.model.StopChargingRequest
import com.toyota.oneapp.features.publiccharging.domain.repo.PublicChargingRepo
import com.toyota.oneapp.features.publiccharging.mock.MockChargeStationData
import com.toyota.oneapp.features.publiccharging.mock.MockSendToCarData
import com.toyota.oneapp.features.publiccharging.mock.getMockStopChargingPayload
import com.toyota.oneapp.features.publiccharging.presentation.model.ChargingSessionStatus
import com.toyota.oneapp.features.publiccharging.presentation.model.toUiModel
import com.toyota.oneapp.features.publiccharging.util.PublicChargingConstants
import com.toyota.oneapp.mockito.any
import com.toyota.oneapp.model.poi.Address
import com.toyota.oneapp.model.poi.LocationDetails
import com.toyota.oneapp.model.poi.StcLocationResponse
import com.toyota.oneapp.model.vehicle.Features
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.ApiError
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.api.manager.UserProfileAPIManager
import com.toyota.oneapp.network.api.repository.LocationRepository
import com.toyota.oneapp.network.models.ApiResponse
import io.grpc.stub.StreamObserver
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.test.UnconfinedTestDispatcher
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.test.setMain
import org.junit.Assert.assertEquals
import org.junit.Assert.assertNotNull
import org.junit.Assert.assertNull
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.Mockito.anyInt
import org.mockito.Mockito.anyString
import org.mockito.Mockito.mock
import org.mockito.Mockito.`when`
import org.mockito.junit.MockitoJUnitRunner

@OptIn(ExperimentalCoroutinesApi::class)
@RunWith(MockitoJUnitRunner::class)
class PublicChargingLogicTest {
    @Mock
    lateinit var publicChargingRepo: PublicChargingRepo

    @Mock
    lateinit var locationRepository: LocationRepository

    @Mock
    lateinit var userProfileAPIManager: UserProfileAPIManager

    private lateinit var publicChargingLogic: PublicChargingLogic

    @OptIn(ExperimentalCoroutinesApi::class)
    private val testDispatcher = UnconfinedTestDispatcher()

    @OptIn(ExperimentalCoroutinesApi::class)
    @Before
    fun setup() {
        Dispatchers.setMain(testDispatcher)
        publicChargingLogic =
            PublicChargingLogic(
                publicChargingRepo = publicChargingRepo,
                locationRepository = locationRepository,
                userProfileAPIManager = userProfileAPIManager,
                removeDisabledChargingNetworksFromChargeStationsInfo = { it },
            )
    }

    @Test
    fun `fetchNearByStations publicCharging enabled stations available chargeStationInfo`() =
        runTest {
            `when`(
                publicChargingRepo.fetchNearbyStations(
                    brand = anyString(),
                    fuelType = anyString(),
                    radius = anyInt(),
                    region = anyString(),
                    latitude = anyOrNull(),
                    longitude = anyOrNull(),
                    vin = anyString(),
                    connector = anyOrNull(),
                    operatorname = anyOrNull(),
                ),
            ).thenReturn(
                Resource.Success(
                    data =
                        StationsListResponse(
                            payload =
                                StationsListPayload(
                                    stations = listOf(getStation()),
                                    totalRecords = 0,
                                ),
                        ),
                ),
            )

            val flowResult = publicChargingLogic.fetchNearByStations(0.0, 0.0, getVehicleInfo())

            flowResult.collect {
                assertEquals("Dallas", it.chargeStationsInfoList[0].evCity)
                assertEquals(
                    2.1,
                    it.chargeStationsInfoList[0]
                        .evTariffInfo
                        .firstOrNull()
                        ?.evElements
                        ?.first()
                        ?.evPriceComponents
                        ?.first()
                        ?.evPrice,
                )
                assertEquals(
                    2,
                    it.chargeStationsInfoList[0]
                        .evTariffInfo
                        .firstOrNull()
                        ?.evElements
                        ?.first()
                        ?.evPriceComponents
                        ?.first()
                        ?.evStepSize,
                )
                assertEquals(
                    "type",
                    it.chargeStationsInfoList[0]
                        .evTariffInfo
                        .firstOrNull()
                        ?.evElements
                        ?.first()
                        ?.evPriceComponents
                        ?.first()
                        ?.evType,
                )
            }
        }

    @Test
    fun `fetchNearByStations publicCharging enabled data null returns empty stations`() =
        runTest {
            `when`(
                publicChargingRepo.fetchNearbyStations(
                    brand = anyString(),
                    fuelType = anyString(),
                    radius = anyInt(),
                    region = anyString(),
                    latitude = anyOrNull(),
                    longitude = anyOrNull(),
                    vin = anyString(),
                    connector = anyOrNull(),
                    operatorname = anyOrNull(),
                ),
            ).thenReturn(
                Resource.Success(
                    data = null,
                ),
            )

            val flowResult = publicChargingLogic.fetchNearByStations(0.0, 0.0, getVehicleInfo())

            flowResult.collect {
                assertEquals(0, it.totalStations)
                assert(it.chargeStationsInfoList.isEmpty())
            }
        }

    @Test
    fun `fetchNearByStations publicCharging enabled payload null returns empty stations`() =
        runTest {
            `when`(
                publicChargingRepo.fetchNearbyStations(
                    brand = anyString(),
                    fuelType = anyString(),
                    radius = anyInt(),
                    region = anyString(),
                    latitude = anyOrNull(),
                    longitude = anyOrNull(),
                    vin = anyString(),
                    connector = anyOrNull(),
                    operatorname = anyOrNull(),
                ),
            ).thenReturn(
                Resource.Success(
                    data =
                        StationsListResponse(
                            payload = null,
                        ),
                ),
            )

            val flowResult = publicChargingLogic.fetchNearByStations(0.0, 0.0, getVehicleInfo())

            flowResult.collect {
                assertEquals(0, it.totalStations)
                assert(it.chargeStationsInfoList.isEmpty())
            }
        }

    @Test
    fun `fetchNearByStations publicCharging enabled stations null returns empty stations`() =
        runTest {
            `when`(
                publicChargingRepo.fetchNearbyStations(
                    brand = anyString(),
                    fuelType = anyString(),
                    radius = anyInt(),
                    region = anyString(),
                    latitude = anyOrNull(),
                    longitude = anyOrNull(),
                    vin = anyString(),
                    connector = anyOrNull(),
                    operatorname = anyOrNull(),
                ),
            ).thenReturn(
                Resource.Success(
                    data =
                        StationsListResponse(
                            payload =
                                StationsListPayload(
                                    stations = null,
                                    totalRecords = 0,
                                ),
                        ),
                ),
            )

            val flowResult = publicChargingLogic.fetchNearByStations(0.0, 0.0, getVehicleInfo())

            flowResult.collect {
                assertEquals(0, it.totalStations)
                assert(it.chargeStationsInfoList.isEmpty())
            }
        }

    @Test
    fun `fetchNearByStations with partnertype ev connect`() =
        runTest {
            `when`(
                publicChargingRepo.fetchNearbyStations(
                    brand = anyString(),
                    fuelType = anyString(),
                    radius = anyInt(),
                    region = anyString(),
                    latitude = anyOrNull(),
                    longitude = anyOrNull(),
                    vin = anyString(),
                    connector = anyOrNull(),
                    operatorname = anyOrNull(),
                ),
            ).thenReturn(
                Resource.Success(
                    data =
                        StationsListResponse(
                            payload =
                                StationsListPayload(
                                    stations = listOf(getStation()),
                                    totalRecords = 0,
                                ),
                        ),
                ),
            )

            val flowResult =
                publicChargingLogic.fetchNearByStations(
                    0.0,
                    0.0,
                    getVehicleInfo(),
                    listOf(R.string.partnerFilterEVConnect),
                    listOf(
                        R.string.nacs,
                        R.string.chademo,
                        R.string.ccs1,
                        R.string.j1772,
                        -1,
                    ),
                )

            flowResult.collect {
                assertEquals("Dallas", it.chargeStationsInfoList[0].evCity)
            }
            verify(publicChargingRepo).fetchNearbyStations(
                brand = eq("T"),
                fuelType = eq("I"),
                radius = eq(10),
                region = eq("US"),
                latitude = eq(0.0),
                longitude = eq(0.0),
                vin = eq("vin"),
                connector = eq("NACS,CCS1,CHADEMO,CCS1,CHADEMO,J1772"),
                operatorname = eq("EV Connect"),
            )
        }

    @Test
    fun `fetchNearByStations with partnertype EVgo`() =
        runTest {
            `when`(
                publicChargingRepo.fetchNearbyStations(
                    brand = anyString(),
                    fuelType = anyString(),
                    radius = anyInt(),
                    region = anyString(),
                    latitude = anyOrNull(),
                    longitude = anyOrNull(),
                    vin = anyString(),
                    connector = anyOrNull(),
                    operatorname = anyOrNull(),
                ),
            ).thenReturn(
                Resource.Success(
                    data =
                        StationsListResponse(
                            payload =
                                StationsListPayload(
                                    stations = listOf(),
                                    totalRecords = 0,
                                ),
                        ),
                ),
            )

            val flowResult =
                publicChargingLogic.fetchNearByStations(
                    0.0,
                    0.0,
                    getVehicleInfo(),
                    listOf(R.string.partnerFilterEVgo),
                )

            flowResult.collect {
                assertTrue(it.chargeStationsInfoList.isEmpty())
            }
            verify(publicChargingRepo).fetchNearbyStations(
                brand = eq("T"),
                fuelType = eq("I"),
                radius = eq(10),
                region = eq("US"),
                latitude = eq(0.0),
                longitude = eq(0.0),
                vin = eq("vin"),
                connector = eq(null),
                operatorname = eq("EVgo"),
            )
        }

    @Test
    fun `fetchNearByStations with partnertype FLO Network`() =
        runTest {
            `when`(
                publicChargingRepo.fetchNearbyStations(
                    brand = anyString(),
                    fuelType = anyString(),
                    radius = anyInt(),
                    region = anyString(),
                    latitude = anyOrNull(),
                    longitude = anyOrNull(),
                    vin = anyString(),
                    connector = anyOrNull(),
                    operatorname = anyOrNull(),
                ),
            ).thenReturn(
                Resource.Success(
                    data =
                        StationsListResponse(
                            payload =
                                StationsListPayload(
                                    stations = listOf(),
                                    totalRecords = 0,
                                ),
                        ),
                ),
            )

            val flowResult =
                publicChargingLogic.fetchNearByStations(
                    0.0,
                    0.0,
                    getVehicleInfo(),
                    listOf(R.string.partnerFilterFLONetwork),
                )

            flowResult.collect {
                assertTrue(it.chargeStationsInfoList.isEmpty())
            }
            verify(publicChargingRepo).fetchNearbyStations(
                brand = eq("T"),
                fuelType = eq("I"),
                radius = eq(10),
                region = eq("US"),
                latitude = eq(0.0),
                longitude = eq(0.0),
                vin = eq("vin"),
                connector = eq(null),
                operatorname = eq("FLO Network,FLO US Network"),
            )
        }

    @Test
    fun `fetchNearByStations with partnertype Greenlots`() =
        runTest {
            `when`(
                publicChargingRepo.fetchNearbyStations(
                    brand = anyString(),
                    fuelType = anyString(),
                    radius = anyInt(),
                    region = anyString(),
                    latitude = anyOrNull(),
                    longitude = anyOrNull(),
                    vin = anyString(),
                    connector = anyOrNull(),
                    operatorname = anyOrNull(),
                ),
            ).thenReturn(
                Resource.Success(
                    data =
                        StationsListResponse(
                            payload =
                                StationsListPayload(
                                    stations = listOf(),
                                    totalRecords = 0,
                                ),
                        ),
                ),
            )

            val flowResult =
                publicChargingLogic.fetchNearByStations(
                    0.0,
                    0.0,
                    getVehicleInfo(),
                    listOf(R.string.partnerFilterGreenlots),
                )

            flowResult.collect {
                assertTrue(it.chargeStationsInfoList.isEmpty())
            }
            verify(publicChargingRepo).fetchNearbyStations(
                brand = eq("T"),
                fuelType = eq("I"),
                radius = eq(10),
                region = eq("US"),
                latitude = eq(0.0),
                longitude = eq(0.0),
                vin = eq("vin"),
                connector = eq(null),
                operatorname = eq("Greenlots"),
            )
        }

    @Test
    fun `fetchNearByStations with partnertype Shell Recharge`() =
        runTest {
            `when`(
                publicChargingRepo.fetchNearbyStations(
                    brand = anyString(),
                    fuelType = anyString(),
                    radius = anyInt(),
                    region = anyString(),
                    latitude = anyOrNull(),
                    longitude = anyOrNull(),
                    vin = anyString(),
                    connector = anyOrNull(),
                    operatorname = anyOrNull(),
                ),
            ).thenReturn(
                Resource.Success(
                    data =
                        StationsListResponse(
                            payload =
                                StationsListPayload(
                                    stations = listOf(),
                                    totalRecords = 0,
                                ),
                        ),
                ),
            )

            val flowResult =
                publicChargingLogic.fetchNearByStations(
                    0.0,
                    0.0,
                    getVehicleInfo(),
                    listOf(R.string.partnerFilterShellRecharge),
                )

            flowResult.collect {
                assertTrue(it.chargeStationsInfoList.isEmpty())
            }
        }

    @Test
    fun `fetchNearByStations with partnertype ionna`() =
        runTest {
            `when`(
                publicChargingRepo.fetchNearbyStations(
                    brand = anyString(),
                    fuelType = anyString(),
                    radius = anyInt(),
                    region = anyString(),
                    latitude = anyOrNull(),
                    longitude = anyOrNull(),
                    vin = anyString(),
                    connector = anyOrNull(),
                    operatorname = anyOrNull(),
                ),
            ).thenReturn(
                Resource.Success(
                    data =
                        StationsListResponse(
                            payload =
                                StationsListPayload(
                                    stations = listOf(),
                                    totalRecords = 0,
                                ),
                        ),
                ),
            )

            val flowResult =
                publicChargingLogic.fetchNearByStations(
                    0.0,
                    0.0,
                    getVehicleInfo(),
                    listOf(R.string.partnerFilterIONNA),
                )

            flowResult.collect {
                assertTrue(it.chargeStationsInfoList.isEmpty())
            }
        }

    @Test
    fun `fetchNearByStations with partnertype ChargePoint`() =
        runTest {
            `when`(
                publicChargingRepo.fetchNearbyStations(
                    brand = anyString(),
                    fuelType = anyString(),
                    radius = anyInt(),
                    region = anyString(),
                    latitude = anyOrNull(),
                    longitude = anyOrNull(),
                    vin = anyString(),
                    connector = anyOrNull(),
                    operatorname = anyOrNull(),
                ),
            ).thenReturn(
                Resource.Success(
                    data =
                        StationsListResponse(
                            payload =
                                StationsListPayload(
                                    stations = listOf(),
                                    totalRecords = 0,
                                ),
                        ),
                ),
            )

            val flowResult =
                publicChargingLogic.fetchNearByStations(
                    0.0,
                    0.0,
                    getVehicleInfo(),
                    listOf(R.string.chargePointLowerCase),
                )

            flowResult.collect {
                assertTrue(it.chargeStationsInfoList.isEmpty())
            }
        }

    @Test
    fun `fetchNearByStations with partnertype tesla`() =
        runTest {
            `when`(
                publicChargingRepo.fetchNearbyStations(
                    brand = anyString(),
                    fuelType = anyString(),
                    radius = anyInt(),
                    region = anyString(),
                    latitude = anyOrNull(),
                    longitude = anyOrNull(),
                    vin = anyString(),
                    connector = anyOrNull(),
                    operatorname = anyOrNull(),
                ),
            ).thenReturn(
                Resource.Success(
                    data =
                        StationsListResponse(
                            payload =
                                StationsListPayload(
                                    stations = listOf(),
                                    totalRecords = 0,
                                ),
                        ),
                ),
            )

            val flowResult =
                publicChargingLogic.fetchNearByStations(
                    0.0,
                    0.0,
                    getVehicleInfo(),
                    listOf(R.string.tesla),
                )

            flowResult.collect {
                assertTrue(it.chargeStationsInfoList.isEmpty())
            }
        }

    @Test
    fun `fetchNearByStations with partnertype Unknown`() =
        runTest {
            val vehicleInfo = VehicleInfo()
            vehicleInfo.features = Features(evPublicChargingControl = Features.ENABLED)
            vehicleInfo.fuelType = "I"
            vehicleInfo.region = "US"
            vehicleInfo.vin = "vin"
            `when`(
                publicChargingRepo.fetchNearbyStations(
                    brand = anyString(),
                    fuelType = anyString(),
                    radius = anyInt(),
                    region = anyString(),
                    latitude = anyOrNull(),
                    longitude = anyOrNull(),
                    vin = anyString(),
                    connector = anyOrNull(),
                    operatorname = anyOrNull(),
                ),
            ).thenReturn(
                Resource.Success(
                    data =
                        StationsListResponse(
                            payload =
                                StationsListPayload(
                                    stations = listOf(),
                                    totalRecords = 0,
                                ),
                        ),
                ),
            )

            val flowResult =
                publicChargingLogic.fetchNearByStations(
                    0.0,
                    0.0,
                    vehicleInfo,
                    listOf(-1),
                )

            flowResult.collect {
                assertTrue(it.chargeStationsInfoList.isEmpty())
            }
            verify(publicChargingRepo).fetchNearbyStations(
                brand = eq("T"),
                fuelType = eq("I"),
                radius = eq(10),
                region = eq("US"),
                latitude = eq(0.0),
                longitude = eq(0.0),
                vin = eq("vin"),
                connector = eq(null),
                operatorname = eq(null),
            )
        }

    @Test
    fun `fetchNearByStations should emit empty list if latitue is null`() =
        runTest {
            publicChargingLogic.fetchNearByStations(null, 0.0, VehicleInfo()).collect {
                assertEquals(true, it.chargeStationsInfoList.isEmpty())
            }
        }

    @Test
    fun `fetchNearByStations should emit empty list if longitue is null`() =
        runTest {
            publicChargingLogic.fetchNearByStations(0.0, null, VehicleInfo()).collect {
                assertEquals(true, it.chargeStationsInfoList.isEmpty())
            }
        }

    @Test
    fun `fetchNearByStations publicCharging enabled stations not available chargeStationInfo`() =
        runTest {
            `when`(
                publicChargingRepo.fetchNearbyStations(
                    brand = anyString(),
                    fuelType = anyString(),
                    radius = anyInt(),
                    region = anyString(),
                    latitude = anyOrNull(),
                    longitude = anyOrNull(),
                    vin = anyString(),
                    connector = anyOrNull(),
                    operatorname = anyOrNull(),
                ),
            ).thenReturn(
                Resource.Success(
                    data =
                        StationsListResponse(
                            payload =
                                StationsListPayload(
                                    stations = listOf(),
                                    totalRecords = 0,
                                ),
                        ),
                ),
            )

            val flowResult = publicChargingLogic.fetchNearByStations(0.0, 0.0, getVehicleInfo())

            flowResult.collect { assertEquals(true, it.chargeStationsInfoList.isEmpty()) }
        }

    @Test
    fun `startCharging response success returns chargingId`() =
        runTest {
            val vehicleInfo =
                VehicleInfo().apply {
                    vin = "1234ABC"
                }
            vehicleInfo.vin = "1234ABC"
            vehicleInfo.brand = "T"

            `when`(
                publicChargingRepo.startCharging(
                    anyString(),
                    anyString(),
                    any(),
                ),
            ).thenReturn(
                Resource.Success(
                    data =
                        StartChargingData(
                            messages = Messages("", "", ""),
                            startChargingPayload =
                                StartChargingPayload(
                                    startChargeResponse = StartChargeResponse("1234", "", ""),
                                ),
                        ),
                ),
            )

            val flowResult =
                publicChargingLogic.startCharging(
                    vehicleInfo,
                    StartChargingRequest("", "", "", "", ""),
                )

            flowResult.collect { result ->
                assertNotNull(result)
                assertEquals("1234", result?.startChargingPayload?.startChargeResponse?.chargingId)
            }
        }

    @Test
    fun `startCharging response failure returns null`() =
        runTest {
            val vehicleInfo =
                VehicleInfo().apply {
                    vin = "1234ABC"
                }
            `when`(
                publicChargingRepo.startCharging(
                    anyString(),
                    anyString(),
                    any(),
                ),
            ).thenReturn(
                Resource.Failure<StartChargingData>(error = ApiError(0, "")),
            )

            val flowResult =
                publicChargingLogic.startCharging(
                    vehicleInfo,
                    StartChargingRequest("", "", "", "", ""),
                )

            flowResult.collect {
                assertNull(it)
            }
        }

    @Test
    fun `test_updateFavoriteLocation success`() =
        runTest {
            val favoriteLocations =
                listOf(
                    LocationDetails(
                        placeId = "1",
                        formattedAddress = "Address 1",
                        name = "Station 1",
                        address =
                            Address(
                                street = "street 1",
                                houseNumber = "house 1",
                                city = "city 1",
                                postalCode = "12345",
                                countryShort = "US",
                                adminRegion = "California",
                                adminRegionShort = "CA",
                            ),
                    ),
                    LocationDetails(
                        placeId = "2",
                        formattedAddress = "Address 2",
                        name = "Station 2",
                        address =
                            Address(
                                street = "street 2",
                                houseNumber = "house 2",
                                city = "city 2",
                                postalCode = "67890",
                                countryShort = "US",
                                adminRegion = "California",
                                adminRegionShort = "CA",
                            ),
                    ),
                )

            val streamObserverCaptor =
                argumentCaptor<StreamObserver<ProfileServiceServer.UpdateUserProfileResponse>>()

            `when`(
                userProfileAPIManager.updateFavouriteLocation(
                    any(),
                    streamObserverCaptor.capture(),
                ),
            ).thenAnswer {
                streamObserverCaptor.firstValue.onNext(
                    ProfileServiceServer.UpdateUserProfileResponse.getDefaultInstance(),
                )
                streamObserverCaptor.firstValue.onCompleted()
            }

            val results = publicChargingLogic.updateFavoriteLocation(favoriteLocations).toList()

            assert(results[0] is ChargeStationFavoriteState.Loading)
            assert(results[1] is ChargeStationFavoriteState.Success)
            assertEquals((results[1] as ChargeStationFavoriteState.Success).data, favoriteLocations)
        }

    @Test
    fun `test_updateFavoriteLocation Error`() =
        runTest {
            val favoriteLocations =
                listOf(
                    LocationDetails(
                        placeId = "1",
                        formattedAddress = "Address 1",
                        name = "Station 1",
                    ),
                )

            val streamObserverCaptor =
                argumentCaptor<StreamObserver<ProfileServiceServer.UpdateUserProfileResponse>>()

            `when`(
                userProfileAPIManager.updateFavouriteLocation(
                    any(),
                    streamObserverCaptor.capture(),
                ),
            ).thenAnswer {
                streamObserverCaptor.firstValue.onError(RuntimeException("Failed to fetch user profile"))
            }

            val results = publicChargingLogic.updateFavoriteLocation(favoriteLocations).toList()

            assert(results[0] is ChargeStationFavoriteState.Loading)
            assert(results[1] is ChargeStationFavoriteState.Error)
            assertEquals(
                "Failed to fetch user profile",
                (results[1] as ChargeStationFavoriteState.Error).message,
            )
        }

    @Test
    fun `test_getFavoriteStation Success`() =
        runTest {
            val favoriteStations =
                listOf(
                    LocationDetails(
                        placeId = "1",
                        formattedAddress = "Address 1",
                        name = "Station 1",
                        address =
                            Address(
                                street = "street 1",
                                houseNumber = "house 1",
                                city = "city 1",
                                postalCode = "12345",
                                countryShort = "US",
                                adminRegion = "California",
                                adminRegionShort = "CA",
                            ),
                        refreshDate = 1680000000,
                        timeStamp = 1690000000,
                    ),
                    LocationDetails(
                        placeId = "2",
                        formattedAddress = "Address 2",
                        name = "Station 2",
                        address =
                            Address(
                                street = "street 2",
                                houseNumber = "house 2",
                                city = "city 2",
                                postalCode = "67890",
                                countryShort = "US",
                                adminRegion = "California",
                                adminRegionShort = "CA",
                            ),
                        refreshDate = 1680000000,
                        timeStamp = 1690000000,
                    ),
                )

            val streamObserverCaptor =
                argumentCaptor<StreamObserver<ProfileServiceServer.GetUserProfileResponse>>()

            val mockResponse = mock<ProfileServiceServer.GetUserProfileResponse>()
            val mockUserProfile = mock<ProfileServiceServer.UserProfile>()
            val mockCommonVehicleSettings =
                mock<ProfileServiceTier1.CommonVehicleSettings>() // FIXED: Corrected package!
            val mockNavigationSettings = mock<ProfileServiceTier1.NavigationSettings>()
            val mockPoiList = mock<ProfileServiceTier1.NavigationSettings.PoiList>()
            val mockRefreshDate =
                mock<com.google.protobuf.Timestamp>().apply {
                    `when`(seconds).thenReturn(1680000000)
                }
            val mockTimestamp =
                mock<com.google.protobuf.Timestamp>().apply {
                    `when`(seconds).thenReturn(1690000000)
                }
            val mockPoiAddress1 =
                mock<ProfileServiceTier1.NavigationSettings.Poi.Address>().apply {
                    `when`(street).thenReturn("street 1")
                    `when`(houseNumber).thenReturn("house 1")
                    `when`(city).thenReturn("city 1")
                    `when`(postalCode).thenReturn("12345")
                    `when`(countryShort).thenReturn("US")
                    `when`(adminRegion).thenReturn("California")
                    `when`(adminRegionShort).thenReturn("CA")
                }
            val mockPoiAddress2 =
                mock<ProfileServiceTier1.NavigationSettings.Poi.Address>().apply {
                    `when`(street).thenReturn("street 2")
                    `when`(houseNumber).thenReturn("house 2")
                    `when`(city).thenReturn("city 2")
                    `when`(postalCode).thenReturn("67890")
                    `when`(countryShort).thenReturn("US")
                    `when`(adminRegion).thenReturn("California")
                    `when`(adminRegionShort).thenReturn("CA")
                }
            val mockFavorites =
                listOf(
                    mock<ProfileServiceTier1.NavigationSettings.Poi>().apply {
                        `when`(placeId).thenReturn("1")
                        `when`(formattedAddress).thenReturn("Address 1")
                        `when`(name).thenReturn("Station 1")
                        `when`(address).thenReturn(mockPoiAddress1)
                        `when`(refreshDate).thenReturn(mockRefreshDate)
                        `when`(timestamp).thenReturn(mockTimestamp)
                    },
                    mock<ProfileServiceTier1.NavigationSettings.Poi>().apply {
                        `when`(placeId).thenReturn("2")
                        `when`(formattedAddress).thenReturn("Address 2")
                        `when`(name).thenReturn("Station 2")
                        `when`(address).thenReturn(mockPoiAddress2)
                        `when`(refreshDate).thenReturn(mockRefreshDate)
                        `when`(timestamp).thenReturn(mockTimestamp)
                    },
                )

            `when`(mockResponse.userProfile).thenReturn(mockUserProfile)
            `when`(mockUserProfile.commonVehicleSettings).thenReturn(mockCommonVehicleSettings)
            `when`(mockCommonVehicleSettings.navigationSettings).thenReturn(mockNavigationSettings)
            `when`(mockNavigationSettings.favorites).thenReturn(mockPoiList)
            `when`(mockPoiList.valueList).thenReturn(mockFavorites)

            `when`(userProfileAPIManager.getUserProfile(streamObserverCaptor.capture()))
                .thenAnswer {
                    streamObserverCaptor.firstValue.onNext(mockResponse)
                    streamObserverCaptor.firstValue.onCompleted()
                }

            val results = publicChargingLogic.getFavoriteStation().toList()

            assert(results[0] is ChargeStationFavoriteState.Loading)
            assert(results[1] is ChargeStationFavoriteState.Success)
            assertEquals(favoriteStations, (results[1] as ChargeStationFavoriteState.Success).data)
        }

    @Test
    fun `test_getFavoriteStation Error`() =
        runTest {
            val streamObserverCaptor =
                argumentCaptor<StreamObserver<ProfileServiceServer.GetUserProfileResponse>>()

            `when`(userProfileAPIManager.getUserProfile(streamObserverCaptor.capture()))
                .thenAnswer {
                    streamObserverCaptor.firstValue.onError(RuntimeException("API Error"))
                }

            val results = publicChargingLogic.getFavoriteStation().toList()

            assert(results[0] is ChargeStationFavoriteState.Loading)
            assert(results[1] is ChargeStationFavoriteState.Error)
            assertEquals((results[1] as ChargeStationFavoriteState.Error).message, "API Error")
        }

    @Test
    fun `test_sendToCar Success`() =
        runTest {
            val mockRequest = MockSendToCarData.getSendPOIToCarRequest()
            val mockResponse = MockSendToCarData.getSuccessResponse()
            `when`(locationRepository.sendPOIToCar(mockRequest))
                .thenReturn(Resource.Success(data = mockResponse))
            val result = publicChargingLogic.sendToCar(mockRequest).toList()
            assert(result[0] is SendToCarState.Init)
            assert(result[1] is SendToCarState.Success)
            assertEquals(mockResponse.payload, (result[1] as SendToCarState.Success).data)
        }

    @Test
    fun `test_sendToCar Success with null payload `() =
        runTest {
            val mockRequest = MockSendToCarData.getSendPOIToCarRequest()
            val responseWithNullPayload = ApiResponse<StcLocationResponse?>(payload = null)

            `when`(locationRepository.sendPOIToCar(mockRequest))
                .thenReturn(Resource.Success(data = responseWithNullPayload))

            val result = publicChargingLogic.sendToCar(mockRequest).toList()

            assert(result[0] is SendToCarState.Init)
            assert(result[1] is SendToCarState.Success)
            assertNull((result[1] as SendToCarState.Success).data)
        }

    @Test
    fun `test_sendToCar Error`() =
        runTest {
            val mockRequest = MockSendToCarData.getSendPOIToCarRequest()

            `when`(locationRepository.sendPOIToCar(mockRequest))
                .thenReturn(
                    Resource.Failure(
                        error =
                            ApiError(
                                code = 500,
                                message = "Internal Server Error",
                            ),
                    ),
                )

            val result = publicChargingLogic.sendToCar(mockRequest).toList()

            assert(result[0] is SendToCarState.Init)
            assert(result[1] is SendToCarState.Error)
            assertEquals("Internal Server Error", (result[1] as SendToCarState.Error).message)
        }

    @Test
    fun `test_sendToCar emits Error with default`() =
        runTest {
            val mockRequest = MockSendToCarData.getSendPOIToCarRequest()

            `when`(locationRepository.sendPOIToCar(mockRequest))
                .thenReturn(
                    Resource.Failure(
                        error =
                            ApiError(
                                code = 400,
                                message = "Unknown Error",
                            ),
                    ),
                )

            val result = publicChargingLogic.sendToCar(mockRequest).toList()

            assert(result[0] is SendToCarState.Init)
            assert(result[1] is SendToCarState.Error)
            assertEquals("Unknown Error", (result[1] as SendToCarState.Error).message)
        }

    @Test
    fun `test evConnectorSum getPlugValue`() {
        val evConnectorSum = MockChargeStationData.getEvConnectorSum()
        val result = evConnectorSum.getPlugValue()
        assertEquals(
            listOf(
                listOf(PublicChargingConstants.CCS1, "2/2"),
                listOf(PublicChargingConstants.J1772, "3/3"),
                listOf(PublicChargingConstants.CHADEMO, "1/1"),
                listOf(PublicChargingConstants.NACS, "1/1"),
            ),
            result,
        )
    }

    private fun getStation() =
        Station(
            accessCode = null,
            address = "",
            chargingWhenClosed = null,
            city = "Dallas",
            connectorSum = null,
            coordinates = null,
            country = "USA",
            partyId = null,
            directions = null,
            evConnectorTypes = null,
            evDCFastNum = null,
            evLevel1 = 0,
            evLevel2 = 0,
            evOther = null,
            evSource = null,
            facilities = null,
            geometry = Geometry("", listOf()),
            id = null,
            isPartner = null,
            lastUpdated = null,
            name = "",
            openingTimes = OpeningTimes(null, "24 hours a day"),
            operator = null,
            otherIndicators = null,
            owner = StationInfo(null, ""),
            parkingType = null,
            partnerInfo =
                listOf(
                    PartnerInfo(
                        evses =
                            arrayListOf(
                                Evses(
                                    connectors =
                                        arrayListOf(
                                            Connectors(
                                                id = "123",
                                                powerType = "powerType",
                                                voltage = 120,
                                                amperage = 30,
                                                pnc = true,
                                            ),
                                        ),
                                ),
                            ),
                        tariffInfo =
                            arrayListOf(
                                TariffInfo(
                                    countryCode = "123",
                                    partyId = "partyId",
                                    currency = "currency",
                                    elements =
                                        arrayListOf(
                                            Elements(
                                                priceComponents =
                                                    arrayListOf(
                                                        PriceComponents(
                                                            price = 2.1,
                                                            stepSize = 2,
                                                            type = "type",
                                                            vat = 3,
                                                        ),
                                                    ),
                                            ),
                                        ),
                                    endDateTime = null,
                                    id = null,
                                    lastUpdated = null,
                                    minPrice = null,
                                    maxPrice = null,
                                    partnerName = null,
                                    startDateTime = null,
                                    tariffAltUrl = null,
                                    tariffType = null,
                                    tariffAltText =
                                        arrayListOf(
                                            TariffAltText(
                                                language = "en",
                                                text = "2.1 per kWh",
                                            ),
                                        ),
                                ),
                            ),
                    ),
                ),
            phoneNumber = "",
            placeId = null,
            postalCode = "12345",
            province = "TX",
            statusSum = null,
            statusCode = null,
            subOperator = null,
            timeZone = null,
            distance = null,
            amenities = null,
            isPreferredPartner = null,
            tariffInfo =
                TariffInfo(
                    countryCode = "123",
                    partyId = "partyId",
                    currency = "currency",
                    elements =
                        arrayListOf(
                            Elements(
                                priceComponents =
                                    arrayListOf(
                                        PriceComponents(
                                            price = 2.1,
                                            stepSize = 2,
                                            type = "type",
                                            vat = 3,
                                        ),
                                    ),
                            ),
                        ),
                    endDateTime = null,
                    id = null,
                    lastUpdated = null,
                    minPrice = null,
                    maxPrice = null,
                    partnerName = null,
                    startDateTime = null,
                    tariffAltUrl = null,
                    tariffType = null,
                ),
        )

    @Test
    fun `stopCharging should return success data`() =
        runTest {
            val stopChargingRequest = StopChargingRequest("id")
            val vehicleInfo = getVehicleInfo()

            val domainData =
                StopChargingData(
                    messages =
                        Messages(
                            responseCode = "200",
                            description = "Stopped successfully",
                            detailedDescription = "Session has ended.",
                        ),
                    payload = getMockStopChargingPayload(),
                )

            val expected = domainData.toUiModel()

            whenever(
                publicChargingRepo.stopCharging(
                    make = anyString(),
                    vin = anyString(),
                    stopChargingRequest = any(),
                ),
            ).thenReturn(Resource.Success(data = domainData))

            val result =
                publicChargingLogic
                    .stopCharging(
                        vehicleInfo = vehicleInfo,
                        stopChargingRequest = stopChargingRequest,
                    ).toList()

            assertEquals(1, result.size)
            val actual = result[0]

            assertNotNull(actual)
            assertEquals(expected, actual)
        }

    @Test
    fun `stopCharging should map ACTIVE status correctly`() =
        runTest {
            val vehicleInfo = getVehicleInfo()
            val stopChargingRequest = StopChargingRequest("id")
            val domainData =
                StopChargingData(
                    messages = Messages("200", "Active session", "Session still running"),
                    payload =
                        getMockStopChargingPayload().copy(
                            session =
                                getMockStopChargingPayload().session.copy(
                                    data =
                                        getMockStopChargingPayload().session.data.copy(
                                            status = "ACTIVE",
                                        ),
                                ),
                        ),
                )

            val expected = domainData.toUiModel()

            whenever(
                publicChargingRepo.stopCharging(
                    make = anyString(),
                    vin = anyString(),
                    stopChargingRequest = any(),
                ),
            ).thenReturn(Resource.Success(data = domainData))

            val result =
                publicChargingLogic
                    .stopCharging(vehicleInfo, stopChargingRequest)
                    .toList()

            assertEquals(1, result.size)
            val actual = result[0]
            assertEquals(expected.status, ChargingSessionStatus.ACTIVE)
            assertEquals(expected, actual)
        }

    @Test
    fun `stopCharging should map INITIATED status correctly`() =
        runTest {
            val vehicleInfo = getVehicleInfo()
            val stopChargingRequest = StopChargingRequest("id")
            val domainData =
                StopChargingData(
                    messages = Messages("200", "Active session", "Session still running"),
                    payload =
                        getMockStopChargingPayload().copy(
                            session =
                                getMockStopChargingPayload().session.copy(
                                    data =
                                        getMockStopChargingPayload().session.data.copy(
                                            status = "INITIATED",
                                        ),
                                ),
                        ),
                )

            val expected = domainData.toUiModel()

            whenever(
                publicChargingRepo.stopCharging(
                    make = anyString(),
                    vin = anyString(),
                    stopChargingRequest = any(),
                ),
            ).thenReturn(Resource.Success(data = domainData))

            val result =
                publicChargingLogic
                    .stopCharging(vehicleInfo, stopChargingRequest)
                    .toList()

            assertEquals(1, result.size)
            val actual = result[0]
            assertEquals(expected.status, ChargingSessionStatus.INITIATED)
            assertEquals(expected, actual)
        }

    @Test
    fun `stopCharging should map REJECTED status correctly`() =
        runTest {
            val vehicleInfo = getVehicleInfo()

            val stopChargingRequest = StopChargingRequest("id")
            val domainData =
                StopChargingData(
                    messages = Messages("200", "Active session", "Session still running"),
                    payload =
                        getMockStopChargingPayload().copy(
                            session =
                                getMockStopChargingPayload().session.copy(
                                    data =
                                        getMockStopChargingPayload().session.data.copy(
                                            status = "REJECTED",
                                        ),
                                ),
                        ),
                )

            val expected = domainData.toUiModel()

            whenever(
                publicChargingRepo.stopCharging(
                    make = anyString(),
                    vin = anyString(),
                    stopChargingRequest = any(),
                ),
            ).thenReturn(Resource.Success(data = domainData))

            val result =
                publicChargingLogic
                    .stopCharging(vehicleInfo, stopChargingRequest)
                    .toList()

            assertEquals(1, result.size)
            val actual = result[0]
            assertEquals(expected.status, ChargingSessionStatus.REJECTED)
            assertEquals(expected, actual)
        }

    @Test
    fun `stopCharging should map UNKNOWN status correctly`() =
        runTest {
            val vehicleInfo = getVehicleInfo()

            val stopChargingRequest = StopChargingRequest("id")
            val domainData =
                StopChargingData(
                    messages = Messages("200", "Active session", "Session still running"),
                    payload =
                        getMockStopChargingPayload().copy(
                            session =
                                getMockStopChargingPayload().session.copy(
                                    data =
                                        getMockStopChargingPayload().session.data.copy(
                                            status = "UNKNOWN",
                                        ),
                                ),
                        ),
                )

            val expected = domainData.toUiModel()

            whenever(
                publicChargingRepo.stopCharging(
                    make = anyString(),
                    vin = anyString(),
                    stopChargingRequest = any(),
                ),
            ).thenReturn(Resource.Success(data = domainData))

            val result =
                publicChargingLogic
                    .stopCharging(vehicleInfo, stopChargingRequest)
                    .toList()

            assertEquals(1, result.size)
            val actual = result[0]
            assertEquals(expected.status, ChargingSessionStatus.UNKNOWN)
            assertEquals(expected, actual)
        }

    private fun getVehicleInfo(): VehicleInfo {
        val vehicleInfo = VehicleInfo()
        vehicleInfo.features = Features(evPublicChargingControl = Features.ENABLED)
        vehicleInfo.fuelType = "I"
        vehicleInfo.region = "US"
        vehicleInfo.vin = "vin"
        vehicleInfo.brand = "T"
        return vehicleInfo
    }
}
