/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.landingpage.domain.logic

import com.toyota.oneapp.features.plugandcharge.landingpage.domain.model.PlugAndChargeToggleStatus
import com.toyota.oneapp.features.plugandcharge.landingpage.domain.repository.PlugAndChargeToggleRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.runTest
import org.junit.Assert.assertEquals
import org.junit.Test

class GetPlugAndChargeToggleStatusLogicTest {
    private lateinit var logic: GetPlugAndChargeToggleStatusLogic

    @Test
    fun `given repository returns success when access to toggle state then logic returns its value`() =
        runTest {
            prepareScenario(true)
            assertEquals(true, logic().first())

            prepareScenario(false)
            assertEquals(false, logic().first())
        }

    private fun prepareScenario(result: Boolean) {
        logic = GetPlugAndChargeToggleStatusLogic(FakePlugAndChargeToggleRepository(result))
    }

    private class FakePlugAndChargeToggleRepository(
        private val result: Boolean,
    ) : PlugAndChargeToggleRepository {
        override val toggleStatus: StateFlow<Result<PlugAndChargeToggleStatus>>
            get() =
                MutableStateFlow(
                    Result.success(
                        PlugAndChargeToggleStatus(
                            isToggleOn = result,
                            expirationDate = null,
                        ),
                    ),
                )

        override suspend fun setToggleStatus(value: Boolean): Result<Unit> = Result.success(Unit)

        override suspend fun updateToggleStatus() {}
    }
}
