/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.enrollment.data.mapper.impl

import com.toyota.oneapp.features.plugandcharge.enrollment.data.dto.PlugAndChargeEnrollmentStatusDto
import com.toyota.oneapp.features.plugandcharge.enrollment.data.dto.certificate.CertificateDetailsDto
import com.toyota.oneapp.features.plugandcharge.enrollment.data.dto.certificate.CertificateTypeDto
import com.toyota.oneapp.features.plugandcharge.enrollment.domain.model.PlugAndChargeEnrollmentStatus
import org.junit.Assert.assertEquals
import org.junit.Test
import java.util.Date

class PlugAndChargeEnrollmentStatusMapperImplTest {
    private val mapper = PlugAndChargeEnrollmentStatusMapperImpl()

    @Test
    fun `when certificates is null, then returns NotStarted`() {
        val result =
            mapper.map(
                PlugAndChargeEnrollmentStatusDto(
                    toggleStatus = null,
                    certificates = null,
                ),
            )

        assertEquals(
            PlugAndChargeEnrollmentStatus.NotEnrolled.NotStarted,
            result,
        )
    }

    @Test
    fun `when provision is missing, then returns NotStarted`() {
        val result =
            mapper.map(
                PlugAndChargeEnrollmentStatusDto(
                    toggleStatus = true,
                    certificates =
                        listOf(
                            CertificateDetailsDto(
                                type = CertificateTypeDto.CONTRACT,
                                isActive = true,
                                progress = null,
                                endDate = null,
                            ),
                        ),
                ),
            )

        assertEquals(
            PlugAndChargeEnrollmentStatus.NotEnrolled.NotStarted,
            result,
        )
    }

    @Test
    fun `when provision is not active, then returns EnrollmentNotPossible`() {
        val result =
            mapper.map(
                PlugAndChargeEnrollmentStatusDto(
                    toggleStatus = false,
                    certificates =
                        listOf(
                            CertificateDetailsDto(
                                type = CertificateTypeDto.PROVISION,
                                isActive = false,
                                progress = null,
                                endDate = null,
                            ),
                        ),
                ),
            )

        assertEquals(
            PlugAndChargeEnrollmentStatus.NotEnrolled.EnrollmentNotPossible,
            result,
        )
    }

    @Test
    fun `when contract is active and toggle is true, then returns Enrolled with toggle true`() {
        val endDate = Date()
        val result =
            mapper.map(
                PlugAndChargeEnrollmentStatusDto(
                    toggleStatus = true,
                    certificates =
                        listOf(
                            CertificateDetailsDto(
                                type = CertificateTypeDto.PROVISION,
                                isActive = true,
                                progress = null,
                                endDate = null,
                            ),
                            CertificateDetailsDto(
                                type = CertificateTypeDto.CONTRACT,
                                isActive = true,
                                progress = null,
                                endDate = endDate,
                            ),
                        ),
                ),
            )

        assertEquals(
            PlugAndChargeEnrollmentStatus.Enrolled(isToggleOn = true, expirationDate = endDate),
            result,
        )
    }

    @Test
    fun `when contract is inactive, then returns Incomplete with progress`() {
        val result =
            mapper.map(
                PlugAndChargeEnrollmentStatusDto(
                    toggleStatus = false,
                    certificates =
                        listOf(
                            CertificateDetailsDto(
                                type = CertificateTypeDto.PROVISION,
                                isActive = true,
                                progress = null,
                                endDate = null,
                            ),
                            CertificateDetailsDto(
                                type = CertificateTypeDto.CONTRACT,
                                isActive = false,
                                progress = 30,
                                endDate = null,
                            ),
                        ),
                ),
            )

        assertEquals(
            PlugAndChargeEnrollmentStatus.NotEnrolled.Incomplete(progress = 30),
            result,
        )
    }

    @Test
    fun `when contract is inactive and progress is null, then returns Incomplete with 0 progress`() {
        val result =
            mapper.map(
                PlugAndChargeEnrollmentStatusDto(
                    toggleStatus = null,
                    certificates =
                        listOf(
                            CertificateDetailsDto(
                                type = CertificateTypeDto.PROVISION,
                                isActive = true,
                                progress = null,
                                endDate = null,
                            ),
                            CertificateDetailsDto(
                                type = CertificateTypeDto.CONTRACT,
                                isActive = false,
                                progress = null,
                                endDate = null,
                            ),
                        ),
                ),
            )

        assertEquals(
            PlugAndChargeEnrollmentStatus.NotEnrolled.Incomplete(progress = 0),
            result,
        )
    }
}
