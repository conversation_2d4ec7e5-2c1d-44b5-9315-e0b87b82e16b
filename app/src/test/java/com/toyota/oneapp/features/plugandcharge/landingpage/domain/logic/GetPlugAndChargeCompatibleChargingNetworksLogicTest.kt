/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.landingpage.domain.logic

import com.toyota.oneapp.features.chargingnetwork.domain.model.ChargingNetworkType
import com.toyota.oneapp.features.chargingnetwork.domain.model.ChargingNetworksState
import com.toyota.oneapp.features.chargingnetwork.domain.model.DataConsentNetworksModel
import com.toyota.oneapp.features.chargingnetwork.domain.model.PlugAndChargeNetworkModel
import com.toyota.oneapp.features.dataconsent.domain.model.DataConsentStatus
import com.toyota.oneapp.features.dataconsent.domain.model.DataConsentStrategy
import com.toyota.oneapp.features.plugandcharge.enrollment.domain.model.PlugAndChargeEnrollmentStatus
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.test.runTest
import org.junit.Assert.assertEquals
import org.junit.Test

class GetPlugAndChargeCompatibleChargingNetworksLogicTest {
    @Test
    fun `emits all enabled and data consent accepted networks`() =
        runTest {
            val compatibleNetworks =
                listOf(
                    ChargingNetworkType.IONNA,
                    ChargingNetworkType.TESLA,
                )
            val enabledChargingNetwork =
                ChargingNetworksState.Loaded(
                    withDataConsent =
                        DataConsentNetworksModel(
                            dataConsentStrategies =
                                listOf(
                                    DataConsentStrategy.IONNA,
                                    DataConsentStrategy.TESLA,
                                ),
                            status = DataConsentStatus.ACCEPTED,
                        ),
                    plugAndChargeNetwork =
                        PlugAndChargeNetworkModel.Loaded(
                            PlugAndChargeEnrollmentStatus.Enrolled(isToggleOn = true, expirationDate = null),
                        ),
                )

            val useCase =
                GetPlugAndChargeCompatibleChargingNetworksLogic(
                    getPlugAndChargeCompatibleChargingNetworksList = {
                        compatibleNetworks
                    },
                    getEnabledChargingNetworks = {
                        flowOf(enabledChargingNetwork)
                    },
                )

            val result = useCase().toList().flatten()

            assertEquals(
                listOf(
                    ChargingNetworkType.IONNA,
                    ChargingNetworkType.TESLA,
                ),
                result,
            )
        }

    @Test
    fun `emits only enabled and data consent accepted networks`() =
        runTest {
            val compatibleNetworks = listOf(ChargingNetworkType.TESLA)
            val enabledChargingNetwork =
                ChargingNetworksState.Loaded(
                    withDataConsent =
                        DataConsentNetworksModel(
                            dataConsentStrategies =
                                listOf(
                                    DataConsentStrategy.IONNA,
                                    DataConsentStrategy.TESLA,
                                ),
                            status = DataConsentStatus.ACCEPTED,
                        ),
                    plugAndChargeNetwork =
                        PlugAndChargeNetworkModel.Loaded(
                            PlugAndChargeEnrollmentStatus.Enrolled(isToggleOn = true, expirationDate = null),
                        ),
                )

            val useCase =
                GetPlugAndChargeCompatibleChargingNetworksLogic(
                    getPlugAndChargeCompatibleChargingNetworksList = {
                        compatibleNetworks
                    },
                    getEnabledChargingNetworks = {
                        flowOf(enabledChargingNetwork)
                    },
                )

            val result = useCase().toList().flatten()

            assertEquals(
                listOf(ChargingNetworkType.TESLA),
                result,
            )
        }

    @Test
    fun `when no compatible networks, emits empty`() =
        runTest {
            val compatibleNetworks = emptyList<ChargingNetworkType>()
            val enabledChargingNetwork =
                ChargingNetworksState.Loaded(
                    withDataConsent =
                        DataConsentNetworksModel(
                            dataConsentStrategies = listOf(DataConsentStrategy.TESLA),
                            status = DataConsentStatus.ACCEPTED,
                        ),
                    plugAndChargeNetwork =
                        PlugAndChargeNetworkModel.Loaded(
                            PlugAndChargeEnrollmentStatus.Enrolled(isToggleOn = true, expirationDate = null),
                        ),
                )

            val useCase =
                GetPlugAndChargeCompatibleChargingNetworksLogic(
                    getPlugAndChargeCompatibleChargingNetworksList = {
                        compatibleNetworks
                    },
                    getEnabledChargingNetworks = {
                        flowOf(enabledChargingNetwork)
                    },
                )

            val result = useCase().toList().flatten()

            assertEquals(
                emptyList<ChargingNetworkType>(),
                result,
            )
        }

    @Test
    fun `when compatible but not enabled networks, emits empty`() =
        runTest {
            val compatibleNetworks = listOf(ChargingNetworkType.TESLA)
            val enabledChargingNetwork =
                ChargingNetworksState.Loaded(
                    withDataConsent =
                        DataConsentNetworksModel(
                            dataConsentStrategies = emptyList(),
                            status = DataConsentStatus.ACCEPTED,
                        ),
                    plugAndChargeNetwork =
                        PlugAndChargeNetworkModel.Loaded(
                            PlugAndChargeEnrollmentStatus.Enrolled(isToggleOn = true, expirationDate = null),
                        ),
                )

            val useCase =
                GetPlugAndChargeCompatibleChargingNetworksLogic(
                    getPlugAndChargeCompatibleChargingNetworksList = {
                        compatibleNetworks
                    },
                    getEnabledChargingNetworks = {
                        flowOf(enabledChargingNetwork)
                    },
                )

            val result = useCase().toList().flatten()

            assertEquals(
                emptyList<ChargingNetworkType>(),
                result,
            )
        }
}
