/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.landing.domain.logic

import com.toyota.oneapp.features.plugandcharge.landing.domain.repository.PlugAndChargeToggleRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.test.runTest
import org.junit.Assert.assertEquals
import org.junit.Test

class GetPlugAndChargeToggleStatusLogicTest {
    private lateinit var logic: GetPlugAndChargeToggleStatusLogic

    @Test
    fun `given repository returns success when access to toggle state then logic returns its value`() =
        runTest {
            prepareScenario(true)
            assertEquals(true, logic())

            prepareScenario(false)
            assertEquals(false, logic())
        }

    private fun prepareScenario(result: Boolean) {
        logic = GetPlugAndChargeToggleStatusLogic(FakePlugAndChargeToggleRepository(result))
    }

    private class FakePlugAndChargeToggleRepository(
        private val result: Boolean,
    ) : PlugAndChargeToggleRepository {
        override val toggleState: StateFlow<Boolean>
            get() = MutableStateFlow(result)

        override fun setToggleState(newValue: Boolean): Result<Unit> = Result.success(Unit)
    }
}
