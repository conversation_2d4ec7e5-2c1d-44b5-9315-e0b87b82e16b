/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.landingpage.domain.logic

import com.toyota.oneapp.features.chargingnetwork.domain.model.ChargingNetworkType
import kotlinx.coroutines.test.runTest
import org.junit.Assert.assertEquals
import org.junit.Test

class GetPlugAndChargeCompatibleChargingNetworksListLogicTest {
    @Test
    fun `only return Tesla for compatible charging network`() =
        runTest {
            val useCase = GetPlugAndChargeCompatibleChargingNetworksListLogic()

            val result = useCase()

            assertEquals(
                listOf(ChargingNetworkType.TESLA),
                result,
            )
        }
}
