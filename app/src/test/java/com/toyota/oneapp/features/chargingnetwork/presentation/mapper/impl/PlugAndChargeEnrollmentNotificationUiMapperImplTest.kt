/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargingnetwork.presentation.mapper.impl

import com.toyota.oneapp.features.chargingnetwork.domain.model.ChargingNetworksState
import com.toyota.oneapp.features.chargingnetwork.domain.model.PlugAndChargeNetworkModel
import com.toyota.oneapp.features.plugandcharge.enrollment.domain.model.PlugAndChargeEnrollmentStatus
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.state.PlugAndChargeEnrollmentState
import org.junit.Assert.assertEquals
import org.junit.Test
import java.util.Date

class PlugAndChargeEnrollmentNotificationUiMapperImplTest {
    private val stateNotified =
        PlugAndChargeEnrollmentState.ContainsError.PowerYourVehicleOn(
            isShowingError = false,
        )
    private val mappedState = PlugAndChargeEnrollmentStatus.Enrolled(true, Date())
    private val mapper = PlugAndChargeEnrollmentNotificationUiMapperImpl { mappedState }

    @Test
    fun `when current state is loaded then returns mappedState`() {
        val currentState =
            ChargingNetworksState.Loaded(
                plugAndChargeNetwork =
                    PlugAndChargeNetworkModel.Loaded(
                        status = PlugAndChargeEnrollmentStatus.NotEnrolled.Incomplete(progress = 0),
                    ),
                withDataConsent = null,
            )

        val result =
            mapper.map(
                stateNotified = stateNotified,
                currentChargingNetworksState = currentState,
            )

        assertEquals(
            currentState.copy(plugAndChargeNetwork = PlugAndChargeNetworkModel.Loaded(mappedState)),
            result,
        )
    }

    @Test
    fun `when current state is not loaded then returns current state unchanged`() {
        val currentState = ChargingNetworksState.Loading(enabledChargingNetworksAmount = 1)

        val result =
            mapper.map(
                stateNotified = stateNotified,
                currentChargingNetworksState = currentState,
            )

        assertEquals(currentState, result)
    }
}
