package com.toyota.oneapp.features.chargingnetwork.domain.logic

import com.toyota.oneapp.features.chargingnetwork.domain.model.ChargingNetworkType
import com.toyota.oneapp.features.chargingnetwork.domain.model.ChargingNetworksState
import com.toyota.oneapp.features.chargingnetwork.domain.model.DataConsentNetworksModel
import com.toyota.oneapp.features.chargingnetwork.domain.model.PlugAndChargeNetworkModel
import com.toyota.oneapp.features.dataconsent.domain.model.DataConsentStatus
import com.toyota.oneapp.features.dataconsent.domain.model.DataConsentStrategy
import com.toyota.oneapp.features.plugandcharge.enrollment.domain.model.PlugAndChargeEnrollmentStatus
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.test.runTest
import org.junit.Assert.assertEquals
import org.junit.Test
import java.util.Date

class GetLoadedEnabledChargingNetworksLogicTest {
    @Test
    fun `when plug and charge is enabled and data consent is accepted, emits loading then loaded`() =
        runTest {
            val expectedDataConsent =
                DataConsentNetworksModel(
                    dataConsentStrategies = listOf(DataConsentStrategy.TESLA),
                    status = DataConsentStatus.ACCEPTED,
                )
            val expectedPlugAndCharge =
                PlugAndChargeNetworkModel.Loaded(
                    status = PlugAndChargeEnrollmentStatus.Enrolled(true, Date()),
                )

            val useCase =
                GetLoadedEnabledChargingNetworksLogic(
                    getEnabledChargingNetworkTypes = {
                        listOf(ChargingNetworkType.PLUG_AND_CHARGE)
                    },
                    getLoadedChargingNetworkWithDataConsent = {
                        expectedDataConsent
                    },
                    getLoadedPlugAndChargeNetwork = { accepted ->
                        assertEquals(true, accepted)
                        expectedPlugAndCharge
                    },
                )

            val result = useCase().toList()

            assertEquals(
                listOf(
                    ChargingNetworksState.Loaded(
                        withDataConsent = expectedDataConsent,
                        plugAndChargeNetwork = PlugAndChargeNetworkModel.Loading,
                    ),
                    ChargingNetworksState.Loaded(
                        withDataConsent = expectedDataConsent,
                        plugAndChargeNetwork = expectedPlugAndCharge,
                    ),
                ),
                result,
            )
        }

    @Test
    fun `when plug and charge is enabled but data consent is null, emits loading then loaded with null network`() =
        runTest {
            val useCase =
                GetLoadedEnabledChargingNetworksLogic(
                    getEnabledChargingNetworkTypes = {
                        listOf(ChargingNetworkType.PLUG_AND_CHARGE)
                    },
                    getLoadedChargingNetworkWithDataConsent = {
                        null
                    },
                    getLoadedPlugAndChargeNetwork = {
                        error("Should not be called")
                    },
                )

            val result = useCase().toList()

            assertEquals(
                listOf(
                    ChargingNetworksState.Loaded(
                        withDataConsent = null,
                        plugAndChargeNetwork = PlugAndChargeNetworkModel.Loading,
                    ),
                    ChargingNetworksState.Loaded(
                        withDataConsent = null,
                        plugAndChargeNetwork = null,
                    ),
                ),
                result,
            )
        }

    @Test
    fun `when plug and charge is not enabled, emits loaded with null plug and charge network`() =
        runTest {
            val expectedDataConsent =
                DataConsentNetworksModel(
                    dataConsentStrategies = listOf(DataConsentStrategy.IONNA),
                    status = DataConsentStatus.REGISTER,
                )

            val useCase =
                GetLoadedEnabledChargingNetworksLogic(
                    getEnabledChargingNetworkTypes = {
                        listOf(ChargingNetworkType.TESLA)
                    },
                    getLoadedChargingNetworkWithDataConsent = {
                        expectedDataConsent
                    },
                    getLoadedPlugAndChargeNetwork = {
                        error("Should not be called")
                    },
                )

            val result = useCase().toList()

            assertEquals(
                listOf(
                    ChargingNetworksState.Loaded(
                        withDataConsent = expectedDataConsent,
                        plugAndChargeNetwork = null,
                    ),
                ),
                result,
            )
        }
}
