package com.toyota.oneapp.features.plugandcharge.enrollment.domain.logic

import com.toyota.oneapp.features.plugandcharge.enrollment.domain.model.PlugAndChargeEnrollmentStatus
import com.toyota.oneapp.features.plugandcharge.enrollment.domain.model.exception.EnrollmentStatusException
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.state.PlugAndChargeEnrollmentState
import kotlinx.coroutines.test.runTest
import org.junit.Assert.assertEquals
import org.junit.Assert.assertTrue
import org.junit.Test

class GetEnrollmentStatusInstallationCompleteLogicTest {
    private lateinit var logic: GetEnrollmentStatusInstallationCompleteLogic

    @Test
    fun `when enrollment is enrolled, then returns InstallationComplete`() =
        runTest {
            prepareScenario(
                result =
                    Result.success(
                        PlugAndChargeEnrollmentStatus.Enrolled(
                            isToggleOn = true,
                            expirationDate = null,
                        ),
                    ),
            )

            val result = logic()

            assertEquals(PlugAndChargeEnrollmentState.InstallationComplete, result.getOrNull())
        }

    @Test
    fun `when enrollment is incomplete, then returns failure with not completed`() =
        runTest {
            prepareScenario(
                result = Result.success(PlugAndChargeEnrollmentStatus.NotEnrolled.Incomplete(progress = 2)),
            )

            val result = logic()

            assertTrue(result.exceptionOrNull() is EnrollmentStatusException.NotCompletedYet)
        }

    @Test
    fun `when enrollment is not started, then returns failure with not completed`() =
        runTest {
            prepareScenario(
                result = Result.success(PlugAndChargeEnrollmentStatus.NotEnrolled.NotStarted),
            )

            val result = logic()

            assertTrue(result.exceptionOrNull() is EnrollmentStatusException.NotCompletedYet)
        }

    @Test
    fun `when enrollment is not possible, then returns failure with not completed`() =
        runTest {
            prepareScenario(
                result = Result.success(PlugAndChargeEnrollmentStatus.NotEnrolled.EnrollmentNotPossible),
            )

            val result = logic()

            assertTrue(result.exceptionOrNull() is EnrollmentStatusException.NotCompletedYet)
        }

    @Test
    fun `when result is failure, then returns connection error`() =
        runTest {
            prepareScenario(
                result = Result.failure(Throwable("Failed to get enrollment")),
            )

            val result = logic()

            assertTrue(result.exceptionOrNull() is EnrollmentStatusException.ConnectionError)
        }

    private fun prepareScenario(result: Result<PlugAndChargeEnrollmentStatus>) {
        logic =
            GetEnrollmentStatusInstallationCompleteLogic(
                getPlugAndChargeEnrollmentStatus = {
                    result
                },
            )
    }
}
