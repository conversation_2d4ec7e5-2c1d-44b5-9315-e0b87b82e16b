/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.landingpage.domain.logic

import com.toyota.oneapp.features.plugandcharge.landingpage.domain.model.PlugAndChargeToggleStatus
import com.toyota.oneapp.features.plugandcharge.landingpage.domain.repository.PlugAndChargeToggleRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.test.runTest
import org.junit.Assert.assertEquals
import org.junit.Test

class SetPlugAndChargeToggleStatusLogicTest {
    private lateinit var logic: SetPlugAndChargeToggleStatusLogic

    @Test
    fun `given repository returns success when set toggle state then logic returns success`() =
        runTest {
            val expectedResult = Result.success(Unit)
            prepareScenario(expectedResult)

            val result = logic(true)

            assertEquals(Result.success(Unit), result)
        }

    private fun prepareScenario(result: Result<Unit>) {
        logic = SetPlugAndChargeToggleStatusLogic(FakePlugAndChargeToggleRepository(result))
    }

    private class FakePlugAndChargeToggleRepository(
        private val result: Result<Unit>,
    ) : PlugAndChargeToggleRepository {
        override val toggleStatus: StateFlow<Result<PlugAndChargeToggleStatus>>
            get() =
                MutableStateFlow(
                    Result.success(
                        PlugAndChargeToggleStatus(
                            isToggleOn = false,
                            expirationDate = null,
                        ),
                    ),
                )

        override suspend fun setToggleStatus(value: Boolean): Result<Unit> = result

        override suspend fun updateToggleStatus() {}
    }
}
