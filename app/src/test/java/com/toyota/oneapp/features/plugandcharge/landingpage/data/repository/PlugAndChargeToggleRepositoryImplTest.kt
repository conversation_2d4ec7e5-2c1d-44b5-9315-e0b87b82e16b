package com.toyota.oneapp.features.plugandcharge.landingpage.data.repository

import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.features.plugandcharge.enrollment.data.api.PlugAndChargeEnrollmentApi
import com.toyota.oneapp.features.plugandcharge.enrollment.data.dto.PlugAndChargeEnrollmentStatusDto
import com.toyota.oneapp.features.plugandcharge.enrollment.data.dto.PlugAndChargeStartEnrollmentRequestDto
import com.toyota.oneapp.features.plugandcharge.enrollment.domain.model.PlugAndChargeEnrollmentStatus
import com.toyota.oneapp.features.plugandcharge.landingpage.data.api.PlugAndChargeToggleApi
import com.toyota.oneapp.features.plugandcharge.landingpage.data.dto.PlugAndChargeToggleRequestDto
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.ApiError
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.NetworkStatus
import com.toyota.oneapp.network.models.ApiResponse
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.TestDispatcher
import kotlinx.coroutines.test.TestScope
import kotlinx.coroutines.test.runTest
import okhttp3.ResponseBody.Companion.toResponseBody
import org.junit.Assert.assertEquals
import org.junit.Assert.assertTrue
import org.junit.Test
import retrofit2.Response

class PlugAndChargeToggleRepositoryImplTest {
    private lateinit var repository: PlugAndChargeToggleRepositoryImpl
    private val testDispatcher: TestDispatcher = StandardTestDispatcher()
    private val testScope = TestScope(testDispatcher)

    private val vehicle = VehicleInfo().apply { vin = MOCKED_VIN }

    @Test
    fun `when selected vehicle exists and API returns success, then returns mapped result`() =
        testScope.runTest {
            val expected = PlugAndChargeEnrollmentStatus.NotEnrolled.NotStarted

            prepareScenario(
                selectedVehicle = vehicle,
                getStatusResponse =
                    Response.success(
                        ApiResponse(
                            payload =
                                PlugAndChargeEnrollmentStatusDto(
                                    toggleStatus = null,
                                    certificates = null,
                                ),
                        ),
                    ),
                mappedResult = expected,
            )

            val result = repository.updateToggleStatus()

            assertEquals(Result.success(expected), result)
        }

    @Test
    fun `when selected vehicle is null, then returns failure`() =
        testScope.runTest {
            prepareScenario(
                selectedVehicle = null,
            )

            repository.updateToggleStatus()
            val result = repository.toggleStatus.first()

            assertTrue(result.isFailure)
        }

    @Test
    fun `when API returns null payload, then returns failure`() =
        testScope.runTest {
            prepareScenario(
                selectedVehicle = vehicle,
                getStatusResponse = Response.success(ApiResponse(payload = null)),
            )

            repository.updateToggleStatus()
            val result = repository.toggleStatus.first()

            assertTrue(result.isFailure)
        }

    @Test
    fun `when API returns error, then returns failure`() =
        testScope.runTest {
            prepareScenario(
                selectedVehicle = vehicle,
                getStatusResponse = Response.error(500, "Internal Server Error".toResponseBody()),
            )

            repository.updateToggleStatus()
            val result = repository.toggleStatus.first()

            assertTrue(result.isFailure)
        }

    @Test
    fun `when vehicle info is complete and API returns success, then returns success`() =
        testScope.runTest {
            prepareScenario(
                selectedVehicle =
                    VehicleInfo().apply {
                        vin = "VIN123"
                        region = "US"
                        brand = "T"
                    },
                networkStatus = NetworkStatus.SUCCESS,
            )

            val result = repository.setToggleStatus(true)

            assertEquals(Result.success(Unit), result)
        }

    @Test
    fun `when vehicle is null, then returns failure for setToggleStatus`() =
        testScope.runTest {
            prepareScenario(
                selectedVehicle = null,
            )

            val result = repository.setToggleStatus(true)

            assertTrue(result.isFailure)
        }

    @Test
    fun `when vin is null, then returns failure for setToggleStatus`() =
        testScope.runTest {
            prepareScenario(
                selectedVehicle =
                    VehicleInfo().apply {
                        vin = null
                        region = "US"
                        brand = "T"
                    },
            )

            val result = repository.setToggleStatus(true)

            assertTrue(result.isFailure)
        }

    @Test
    fun `when region is null, then returns failure for setToggleStatus`() =
        testScope.runTest {
            prepareScenario(
                selectedVehicle =
                    VehicleInfo().apply {
                        vin = "VIN123"
                        region = null
                        brand = "T"
                    },
            )

            val result = repository.setToggleStatus(true)

            assertTrue(result.isFailure)
        }

    @Test
    fun `when toggle API returns error, then returns failure`() =
        testScope.runTest {
            prepareScenario(
                selectedVehicle =
                    VehicleInfo().apply {
                        vin = "VIN123"
                        region = "US"
                        brand = "T"
                    },
                networkStatus = NetworkStatus.FAILED,
            )

            val result = repository.setToggleStatus(true)

            assertTrue(result.isFailure)
        }

    private fun prepareScenario(
        selectedVehicle: VehicleInfo? = vehicle,
        getStatusResponse: Response<ApiResponse<PlugAndChargeEnrollmentStatusDto>?>? = null,
        mappedResult: PlugAndChargeEnrollmentStatus = PlugAndChargeEnrollmentStatus.NotEnrolled.NotStarted,
        networkStatus: NetworkStatus = NetworkStatus.SUCCESS,
    ) {
        repository =
            PlugAndChargeToggleRepositoryImpl(
                errorParser = FakeErrorMessageParser(),
                ioContext = testDispatcher,
                enrollmentApi =
                    FakePlugAndChargeEnrollmentApi(
                        getStatusResponse = getStatusResponse,
                        initiateEnrollmentStatus = networkStatus,
                    ),
                toggleApi =
                    FakePlugAndChargeToggleApi(
                        toggleStatus = networkStatus,
                    ),
                statusMapper = { mappedResult },
                applicationData =
                    ApplicationData().apply {
                        setSelectedVehicle(selectedVehicle)
                    },
            )
    }

    private class FakeErrorMessageParser : ErrorMessageParser {
        override fun onNetworkFailure(throwable: Throwable): ApiError =
            ApiError(
                code = 0,
                message = "network error",
                isNetworkError = true,
            )

        override fun <T> onApiCallFailure(response: Response<T>): ApiError =
            ApiError(
                code = response.code(),
                message = "api error",
                isNetworkError = false,
            )
    }

    private class FakePlugAndChargeEnrollmentApi(
        private val getStatusResponse: Response<ApiResponse<PlugAndChargeEnrollmentStatusDto>?>?,
        private val initiateEnrollmentStatus: NetworkStatus,
    ) : PlugAndChargeEnrollmentApi {
        override suspend fun getPlugAndChargeEnrollmentStatus(vin: String): Response<ApiResponse<PlugAndChargeEnrollmentStatusDto>?> =
            getStatusResponse ?: error("getStatus should not be called")

        override suspend fun initiateEnrollment(
            vin: String,
            region: String,
            brand: String,
            body: PlugAndChargeStartEnrollmentRequestDto,
        ): Response<ApiResponse<Unit>?> =
            if (initiateEnrollmentStatus == NetworkStatus.SUCCESS) {
                Response.success(ApiResponse(payload = Unit))
            } else {
                Response.error(500, "error".toResponseBody())
            }
    }

    private class FakePlugAndChargeToggleApi(
        private val toggleStatus: NetworkStatus,
    ) : PlugAndChargeToggleApi {
        override suspend fun setPlugAndChargeToggleStatus(
            vin: String,
            region: String,
            brand: String,
            make: String,
            body: PlugAndChargeToggleRequestDto,
        ): Response<ApiResponse<Unit>?> =
            if (toggleStatus == NetworkStatus.SUCCESS) {
                Response.success(ApiResponse(payload = Unit))
            } else {
                Response.error(500, "error".toResponseBody())
            }
    }

    companion object {
        private const val MOCKED_VIN = "123VIN"
    }
}
