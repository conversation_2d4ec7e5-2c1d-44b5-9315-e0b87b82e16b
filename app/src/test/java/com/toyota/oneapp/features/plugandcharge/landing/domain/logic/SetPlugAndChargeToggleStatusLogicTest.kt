/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.landing.domain.logic

import com.toyota.oneapp.features.plugandcharge.landing.domain.repository.PlugAndChargeToggleRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.test.runTest
import org.junit.Assert.assertEquals
import org.junit.Test

class SetPlugAndChargeToggleStatusLogicTest {
    private lateinit var logic: SetPlugAndChargeToggleStatusLogic

    @Test
    fun `given repository returns success when set toggle state then logic returns success`() =
        runTest {
            val expectedResult = Result.success(Unit)
            prepareScenario(expectedResult)

            val result = logic(true)

            assertEquals(Result.success(Unit), result)
        }

    private fun prepareScenario(result: Result<Unit>) {
        logic = SetPlugAndChargeToggleStatusLogic(FakePlugAndChargeToggleRepository(result))
    }

    private class FakePlugAndChargeToggleRepository(
        private val result: Result<Unit>,
    ) : PlugAndChargeToggleRepository {
        override val toggleState: StateFlow<Boolean>
            get() = MutableStateFlow(true)

        override fun setToggleState(newValue: Boolean): Result<Unit> = result
    }
}
