/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.landingpage.data.api

import com.toyota.oneapp.features.plugandcharge.landingpage.data.dto.PlugAndChargeToggleRequestDto
import com.toyota.oneapp.network.models.ApiResponse
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.Header
import retrofit2.http.POST

interface PlugAndChargeToggleApi {
    @POST(BASE)
    suspend fun setPlugAndChargeToggleStatus(
        @Header("x-vin") vin: String,
        @Header("x-region") region: String,
        @Header("x-brand") brand: String,
        @Header("x-make") make: String,
        @Body body: PlugAndChargeToggleRequestDto,
    ): Response<ApiResponse<Unit>?>

    companion object {
        private const val BASE = "charging/v2/pnc/toggle"
    }
}
