/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.landingpage.presentation.composable

import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.unit.dp
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.DrawableImage
import com.toyota.oneapp.features.core.theme.ThemeMode
import com.toyota.oneapp.features.core.util.ContentPreview
import com.toyota.oneapp.features.core.util.OAThemePreviewProvider

@Composable
fun PlugAndChargeCompatibleStationCardComposable(
    drawableId: Int,
    contentDescriptionId: Int,
) {
    DrawableImage(
        Modifier.size(72.dp, 72.dp),
        drawableId = drawableId,
        contentDescriptionId = contentDescriptionId,
    )
}

@Composable
@Preview
private fun PlugAndChargeCompatibleStationCardComposablePreview(
    @PreviewParameter(OAThemePreviewProvider::class)
    themeMode: ThemeMode,
) {
    ContentPreview(
        modifier = Modifier.wrapContentSize(),
        themeMode = themeMode,
    ) {
        PlugAndChargeCompatibleStationCardComposable(
            drawableId = R.drawable.tesla,
            contentDescriptionId = R.string.tesla,
        )
    }
}
