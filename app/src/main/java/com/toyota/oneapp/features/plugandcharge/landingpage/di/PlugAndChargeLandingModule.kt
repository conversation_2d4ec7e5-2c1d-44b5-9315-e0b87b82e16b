/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.landingpage.di

import com.toyota.oneapp.features.plugandcharge.landingpage.data.repository.PlugAndChargeToggleRepositoryImpl
import com.toyota.oneapp.features.plugandcharge.landingpage.domain.logic.GetPlugAndChargeCompatibleChargingNetworksListLogic
import com.toyota.oneapp.features.plugandcharge.landingpage.domain.logic.GetPlugAndChargeCompatibleChargingNetworksLogic
import com.toyota.oneapp.features.plugandcharge.landingpage.domain.logic.GetPlugAndChargeToggleStatusLogic
import com.toyota.oneapp.features.plugandcharge.landingpage.domain.logic.SetPlugAndChargeToggleStatusLogic
import com.toyota.oneapp.features.plugandcharge.landingpage.domain.repository.PlugAndChargeToggleRepository
import com.toyota.oneapp.features.plugandcharge.landingpage.domain.usecase.GetPlugAndChargeCompatibleChargingNetworksListUseCase
import com.toyota.oneapp.features.plugandcharge.landingpage.domain.usecase.GetPlugAndChargeCompatibleChargingNetworksUseCase
import com.toyota.oneapp.features.plugandcharge.landingpage.domain.usecase.GetPlugAndChargeToggleStatusUseCase
import com.toyota.oneapp.features.plugandcharge.landingpage.domain.usecase.SetPlugAndChargeToggleStatusUseCase
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
interface PlugAndChargeLandingModule {
    @Binds
    fun bindGetPlugAndChargeCompatibleChargingNetworksListUseCase(
        logic: GetPlugAndChargeCompatibleChargingNetworksListLogic,
    ): GetPlugAndChargeCompatibleChargingNetworksListUseCase

    @Binds
    fun bindGetPlugAndChargeCompatibleChargingNetworksUseCase(
        logic: GetPlugAndChargeCompatibleChargingNetworksLogic,
    ): GetPlugAndChargeCompatibleChargingNetworksUseCase

    @Binds
    fun bindGetPlugAndChargeToggleStatusUseCase(logic: GetPlugAndChargeToggleStatusLogic): GetPlugAndChargeToggleStatusUseCase

    @Binds
    fun bindSetPlugAndChargeToggleStatusUseCase(logic: SetPlugAndChargeToggleStatusLogic): SetPlugAndChargeToggleStatusUseCase

    @Binds
    fun bindPlugAndChargeToggleRepository(impl: PlugAndChargeToggleRepositoryImpl): PlugAndChargeToggleRepository
}
