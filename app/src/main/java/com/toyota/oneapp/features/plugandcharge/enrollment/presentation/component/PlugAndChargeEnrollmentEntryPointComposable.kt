/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.enrollment.presentation.component

import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import com.toyota.oneapp.R
import com.toyota.oneapp.features.chargingnetwork.presentation.component.ChargingNetworkStatusButton
import com.toyota.oneapp.features.core.composable.button.DisabledButton
import com.toyota.oneapp.features.core.theme.ThemeMode
import com.toyota.oneapp.features.core.util.ContentPreview
import com.toyota.oneapp.features.plugandcharge.enrollment.domain.model.PlugAndChargeEnrollmentStatus
import java.util.Date

@Composable
fun PlugAndChargeEnrollmentEntryPointComposable(
    modifier: Modifier = Modifier,
    status: PlugAndChargeEnrollmentStatus,
    onOpenEnrollmentPageClick: () -> Unit,
    onPlugAndChargeStatusClick: () -> Unit,
) {
    val activateResourceId = R.string.activate_plug_and_charge_text
    val viewResourceId = R.string.view_plug_and_charge_text
    when (status) {
        is PlugAndChargeEnrollmentStatus.Enrolled ->
            ChargingNetworkStatusButton(
                modifier = modifier,
                stringResourceId = viewResourceId,
                onClick = onPlugAndChargeStatusClick,
            )

        is PlugAndChargeEnrollmentStatus.NotEnrolled.Incomplete ->
            ChargingNetworkStatusButton(
                modifier = modifier,
                stringResourceId = viewResourceId,
                onClick = onOpenEnrollmentPageClick,
            )

        PlugAndChargeEnrollmentStatus.NotEnrolled.NotStarted ->
            ChargingNetworkStatusButton(
                modifier = modifier,
                stringResourceId = activateResourceId,
                onClick = onOpenEnrollmentPageClick,
            )

        PlugAndChargeEnrollmentStatus.NotEnrolled.EnrollmentNotPossible ->
            DisabledButton(
                modifier = modifier,
                textId = activateResourceId,
            )
    }
}

@Composable
@Preview
private fun PlugAndChargeEnrollmentEntryPointComposablePreview(
    @PreviewParameter(LoadedPlugAndChargeNetworkRowPreviewProvider::class)
    parameter: LoadedPlugAndChargeNetworkRowPreviewParameter,
) {
    ContentPreview(
        themeMode = parameter.themeMode,
        modifier = Modifier.wrapContentSize(),
    ) {
        PlugAndChargeEnrollmentEntryPointComposable(
            status = parameter.status,
            onOpenEnrollmentPageClick = {},
            onPlugAndChargeStatusClick = {},
        )
    }
}

private class LoadedPlugAndChargeNetworkRowPreviewProvider :
    PreviewParameterProvider<LoadedPlugAndChargeNetworkRowPreviewParameter> {
    override val values: Sequence<LoadedPlugAndChargeNetworkRowPreviewParameter> get() {
        val enrollmentNotPossible = PlugAndChargeEnrollmentStatus.NotEnrolled.EnrollmentNotPossible
        val enrolled = PlugAndChargeEnrollmentStatus.Enrolled(true, Date())
        return sequenceOf(
            LoadedPlugAndChargeNetworkRowPreviewParameter(
                themeMode = ThemeMode.Dark,
                status = enrollmentNotPossible,
            ),
            LoadedPlugAndChargeNetworkRowPreviewParameter(
                themeMode = ThemeMode.Dark,
                status = enrolled,
            ),
            LoadedPlugAndChargeNetworkRowPreviewParameter(
                themeMode = ThemeMode.Dark,
                status = enrollmentNotPossible,
            ),
            LoadedPlugAndChargeNetworkRowPreviewParameter(
                themeMode = ThemeMode.Light,
                status = enrollmentNotPossible,
            ),
            LoadedPlugAndChargeNetworkRowPreviewParameter(
                themeMode = ThemeMode.Light,
                status = enrolled,
            ),
            LoadedPlugAndChargeNetworkRowPreviewParameter(
                themeMode = ThemeMode.Light,
                status = enrollmentNotPossible,
            ),
        )
    }
}

private data class LoadedPlugAndChargeNetworkRowPreviewParameter(
    val themeMode: ThemeMode,
    val status: PlugAndChargeEnrollmentStatus,
)
