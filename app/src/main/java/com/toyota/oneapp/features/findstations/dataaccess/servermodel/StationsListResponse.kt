/*
 *  Created by sudhan.ram on 07/10/24, 11:16 am
 *  Copyright (c) 2024 . All rights reserved.
 *  Last modified 07/10/24, 11:14 am
 *
 */

package com.toyota.oneapp.features.findstations.dataaccess.servermodel

import com.google.gson.TypeAdapter
import com.google.gson.annotations.JsonAdapter
import com.google.gson.annotations.SerializedName
import com.google.gson.stream.JsonReader
import com.google.gson.stream.JsonWriter
import com.toyota.oneapp.network.models.BaseResponse

data class StationsListResponse(
    @SerializedName("payload") val payload: StationsListPayload?,
) : BaseResponse()

data class StationsListPayload(
    @SerializedName("stations") val stations: List<Station>?,
    @SerializedName("total_no_of_records") val totalRecords: Int?,
)

data class Station(
    @SerializedName("access_code") val accessCode: String?,
    @SerializedName("address") val address: String?,
    @SerializedName("charging_when_closed") val chargingWhenClosed: String?,
    @SerializedName("city") val city: String?,
    @SerializedName("connectorsum") val connectorSum: Connectorsum?,
    @SerializedName("coordinates") val coordinates: List<Double>?,
    @SerializedName("country") val country: String?,
    @SerializedName("party_id") val partyId: String?,
    @SerializedName("directions") val directions: Any?,
    @SerializedName("ev_connector_types") val evConnectorTypes: List<String>?,
    @SerializedName("ev_dc_fast_num") val evDCFastNum: Int?,
    @SerializedName("ev_level1_evse_num") val evLevel1: Int = 0,
    @SerializedName("ev_level2_evse_num") val evLevel2: Int = 0,
    @SerializedName("ev_other_evse") val evOther: String?,
    @SerializedName("ev_source") val evSource: String?,
    @SerializedName("facilities") val facilities: List<String>?,
    @SerializedName("geometry") val geometry: Geometry?,
    @SerializedName("id") val id: String?,
    @SerializedName("is_partner") val isPartner: Boolean?,
    @SerializedName("last_updated") val lastUpdated: String?,
    @SerializedName("name") val name: String?,
    @SerializedName("opening_times") val openingTimes: OpeningTimes?,
    @SerializedName("operator") val operator: StationInfo?,
    @SerializedName("other_indicators") val otherIndicators: String?,
    @SerializedName("owner") val owner: StationInfo?,
    @SerializedName("parking_type") val parkingType: String?,
    @SerializedName("partner_info") val partnerInfo: List<PartnerInfo>?,
    @SerializedName("phone_number") val phoneNumber: String?,
    @SerializedName("place_id") val placeId: String?,
    @SerializedName("postal_code") val postalCode: String?,
    @SerializedName("province") val province: String?,
    @SerializedName("status_sum") val statusSum: String?,
    @SerializedName("status_code") val statusCode: String?,
    @SerializedName("sub_operator") val subOperator: StationInfo?,
    @SerializedName("time_zone") val timeZone: String?,
    @SerializedName("distance") val distance: Double?,
    @SerializedName("amenities") val amenities: List<String>?,
    @SerializedName("is_preferred_partner") val isPreferredPartner: Boolean?,
    @SerializedName("tariff_info") var tariffInfo: TariffInfo?,
)

data class Geometry(
    @SerializedName("type") val type: String?,
    @SerializedName("coordinates") val coordinates: List<Double>?,
)

data class OpeningTimes(
    @SerializedName("regular_hour") val regularHour: String?,
    @SerializedName("timing") val timing: String?,
)

data class StationInfo(
    @SerializedName("name") val name: String?,
    @SerializedName("website") val website: String?,
)

data class ConnectorSumValues(
    @SerializedName("total") var total: Int? = null,
    @SerializedName("active") var active: Int? = null,
)

data class Connectors(
    @SerializedName("amperage") var amperage: Int? = null,
    @SerializedName("format") var format: String? = null,
    @SerializedName("id") var id: String? = null,
    @SerializedName("last_updated") var lastUpdated: String? = null,
    @SerializedName("max_power") var maxPower: Double? = null,
    @SerializedName("power_type") var powerType: String? = null,
    @SerializedName("standard") var standard: String? = null,
    @SerializedName("tariff_id") var tariffId: String? = null,
    @SerializedName("voltage") var voltage: Int? = null,
    @SerializedName("charger_level") var chargerLevel: Int? = null,
    @SerializedName("charger_type") var chargerType: String? = null,
    @SerializedName("pnc") @JsonAdapter(BooleanIntAdapter::class) var pnc: Boolean? = null,
)

data class Connectorsum(
    @SerializedName("CCS1") var ccs1: ConnectorSumValues? = ConnectorSumValues(),
    @SerializedName("CHADEMO") var chademo: ConnectorSumValues? = ConnectorSumValues(),
    @SerializedName("NACS") var nacs: ConnectorSumValues? = ConnectorSumValues(),
    @SerializedName("J1772") var j1772: ConnectorSumValues? = ConnectorSumValues(),
)

data class Coordinates(
    @SerializedName("latitude") var latitude: String? = null,
    @SerializedName("longitude") var longitude: String? = null,
)

data class Directions(
    @SerializedName("language") var language: String? = null,
    @SerializedName("text") var text: String? = null,
)

data class DriverGroups(
    @SerializedName("id") var id: Int? = null,
)

data class Elements(
    @SerializedName("price_components") var priceComponents: ArrayList<PriceComponents> = arrayListOf(),
    @SerializedName("restrictions") var restrictions: ArrayList<Restrictions> = arrayListOf(),
)

data class Evses(
    @SerializedName("capabilities") var capabilities: ArrayList<String> = arrayListOf(),
    @SerializedName("connectors") var connectors: ArrayList<Connectors> = arrayListOf(),
    @SerializedName("coordinates") var coordinates: Coordinates? = Coordinates(),
    @SerializedName("directions") var directions: ArrayList<Directions> = arrayListOf(),
    @SerializedName("evse_id") var evseId: String? = null,
    @SerializedName("floor_level") var floorLevel: String? = null,
    @SerializedName("last_updated") var lastUpdated: String? = null,
    @SerializedName("parking_restrictions") var parkingRestrictions: ArrayList<String> = arrayListOf(),
    @SerializedName("physical_reference") var physicalReference: String? = null,
    @SerializedName("status") var status: String? = null,
    @SerializedName("uid") var uid: String? = null,
)

data class MaxPrice(
    @SerializedName("excl_vat") var exclVat: Int? = null,
    @SerializedName("incl_vat") var inclVat: Int? = null,
)

data class MinPrice(
    @SerializedName("excl_vat") var exclVat: Int? = null,
    @SerializedName("incl_vat") var inclVat: Int? = null,
)

data class PartnerInfo(
    @SerializedName("evses") var evses: ArrayList<Evses> = arrayListOf(),
    @SerializedName("id") var id: String? = null,
    @SerializedName("name") var name: String? = null,
    @SerializedName("tariff_info") var tariffInfo: ArrayList<TariffInfo> = arrayListOf(),
)

data class PriceComponents(
    @SerializedName("price") var price: Double? = null,
    @SerializedName("step_size") var stepSize: Int? = null,
    @SerializedName("type") var type: String? = null,
    @SerializedName("vat") var vat: Int? = null,
)

data class Restrictions(
    @SerializedName("start_time") var startTime: String? = null,
    @SerializedName("end_time") var endTime: String? = null,
    @SerializedName("start_date") var startDate: String? = null,
    @SerializedName("end_date") var endDate: String? = null,
    @SerializedName("min_kwh") var minKwh: Int? = null,
    @SerializedName("max_kwh") var maxKwh: Int? = null,
    @SerializedName("min_current") var minCurrent: Int? = null,
    @SerializedName("max_current") var maxCurrent: Int? = null,
    @SerializedName("min_power") var minPower: Int? = null,
    @SerializedName("max_power") var maxPower: Int? = null,
    @SerializedName("min_duration") var minDuration: Int? = null,
    @SerializedName("max_duration") var maxDuration: Int? = null,
    @SerializedName("day_of_week") var dayOfWeek: String? = null,
    @SerializedName("reservation") var reservation: String? = null,
)

data class TariffAltText(
    @SerializedName("language") var language: String? = null,
    @SerializedName("text") var text: String? = null,
)

data class TariffInfo(
    @SerializedName("country_code") var countryCode: String? = null,
    @SerializedName("party_id") var partyId: String? = null,
    @SerializedName("currency") var currency: String? = null,
    @SerializedName("driver_groups") var driverGroups: ArrayList<DriverGroups> = arrayListOf(),
    @SerializedName("elements") var elements: ArrayList<Elements> = arrayListOf(),
    @SerializedName("end_date_time") var endDateTime: String? = null,
    @SerializedName("id") var id: String? = null,
    @SerializedName("last_updated") var lastUpdated: String? = null,
    @SerializedName("min_price") var minPrice: MinPrice? = MinPrice(),
    @SerializedName("max_price") var maxPrice: MaxPrice? = MaxPrice(),
    @SerializedName("partner_name") var partnerName: String? = null,
    @SerializedName("start_date_time") var startDateTime: String? = null,
    @SerializedName("tariff_alt_text") var tariffAltText: ArrayList<TariffAltText> = arrayListOf(),
    @SerializedName("tariff_alt_url") var tariffAltUrl: String? = null,
    @SerializedName("tariff_type") var tariffType: String? = null,
)

class BooleanIntAdapter : TypeAdapter<Boolean>() {
    override fun write(
        out: JsonWriter,
        value: Boolean?,
    ) {
        out.value(if (value == true) 1 else 0)
    }

    override fun read(reader: JsonReader): Boolean {
        return when (reader.nextInt()) {
            1 -> true
            0 -> false
            else -> false
        }
    }
}
