package com.toyota.oneapp.features.plugandcharge.landingpage.domain.repository

import com.toyota.oneapp.features.plugandcharge.landingpage.domain.model.PlugAndChargeToggleStatus
import kotlinx.coroutines.flow.StateFlow

interface PlugAndChargeToggleRepository {
    val toggleStatus: StateFlow<Result<PlugAndChargeToggleStatus>>

    suspend fun setToggleStatus(value: Boolean): Result<Unit>

    suspend fun updateToggleStatus()
}
