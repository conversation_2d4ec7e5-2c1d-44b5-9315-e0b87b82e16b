/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.landingpage.presentation

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.material.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.unit.dp
import com.toyota.oneapp.features.core.theme.AppTheme.colors
import com.toyota.oneapp.features.core.theme.ThemeMode
import com.toyota.oneapp.features.core.util.ContentPreview
import com.toyota.oneapp.features.core.util.OAThemePreviewProvider
import com.toyota.oneapp.features.plugandcharge.landingpage.presentation.composable.PlugAndChargeLandingScreenContentComposable
import com.toyota.oneapp.features.plugandcharge.landingpage.presentation.composable.PlugAndChargeLandingTopBarComposable

@Composable
fun PlugAndChargeLandingScreen(onBack: () -> Unit) {
    val backgroundColor = colors.tertiary15
    Scaffold(
        topBar = {
            PlugAndChargeLandingTopBarComposable(
                modifier = Modifier
                    .background(backgroundColor)
                    .padding(16.dp),
                onBack = onBack,
            )
        },
    ) { paddingValues ->
        PlugAndChargeLandingScreenContentComposable(
            modifier =
                Modifier
                    .background(backgroundColor)
                    .padding(paddingValues)
                    .padding(horizontal = 16.dp)
                    .padding(top = 32.dp)
                    .fillMaxSize(),
        )
    }
}

@Composable
@Preview
private fun PlugAndChargeLandingScreenPreview(
    @PreviewParameter(OAThemePreviewProvider::class)
    themeMode: ThemeMode,
) {
    ContentPreview(
        modifier = Modifier.wrapContentSize(),
        themeMode = themeMode,
    ) {
        PlugAndChargeLandingScreen(onBack = {})
    }
}
