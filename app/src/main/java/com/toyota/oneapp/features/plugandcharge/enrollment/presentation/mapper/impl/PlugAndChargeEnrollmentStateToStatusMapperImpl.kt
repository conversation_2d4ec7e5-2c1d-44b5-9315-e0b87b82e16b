/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.enrollment.presentation.mapper.impl

import com.toyota.oneapp.features.plugandcharge.enrollment.domain.model.PlugAndChargeEnrollmentStatus
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.mapper.PlugAndChargeEnrollmentStateToStatusMapper
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.state.PlugAndChargeEnrollmentState
import javax.inject.Inject

class PlugAndChargeEnrollmentStateToStatusMapperImpl
    @Inject
    constructor() :
    PlugAndChargeEnrollmentStateToStatusMapper {
        override fun map(state: PlugAndChargeEnrollmentState): PlugAndChargeEnrollmentStatus =
            when (state) {
                is PlugAndChargeEnrollmentState.ContainsError.ActivatingPlugAndCharge,
                is PlugAndChargeEnrollmentState.ContainsError.ConnectingToVehicle,
                ->
                    PlugAndChargeEnrollmentStatus.NotEnrolled.Incomplete(
                        progress = 0,
                    )

                is PlugAndChargeEnrollmentState.ContainsError.PowerYourVehicleOn ->
                    PlugAndChargeEnrollmentStatus.NotEnrolled.NotStarted
                PlugAndChargeEnrollmentState.InstallationComplete ->
                    PlugAndChargeEnrollmentStatus.Enrolled(
                        isToggleOn = false,
                        expirationDate = null,
                    )
            }
    }
