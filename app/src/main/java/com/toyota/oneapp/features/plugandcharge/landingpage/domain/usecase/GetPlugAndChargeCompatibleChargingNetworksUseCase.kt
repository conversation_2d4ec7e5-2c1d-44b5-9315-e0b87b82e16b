/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.landingpage.domain.usecase

import com.toyota.oneapp.features.chargingnetwork.domain.model.ChargingNetworkType
import kotlinx.coroutines.flow.Flow

fun interface GetPlugAndChargeCompatibleChargingNetworksUseCase {
    operator fun invoke(): Flow<List<ChargingNetworkType>>
}
