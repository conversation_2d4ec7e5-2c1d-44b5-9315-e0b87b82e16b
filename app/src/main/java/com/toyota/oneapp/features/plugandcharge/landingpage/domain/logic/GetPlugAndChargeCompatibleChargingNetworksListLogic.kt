/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.landingpage.domain.logic

import com.toyota.oneapp.features.chargingnetwork.domain.model.ChargingNetworkType
import com.toyota.oneapp.features.plugandcharge.landingpage.domain.usecase.GetPlugAndChargeCompatibleChargingNetworksListUseCase
import javax.inject.Inject

class GetPlugAndChargeCompatibleChargingNetworksListLogic
    @Inject
    constructor() : GetPlugAndChargeCompatibleChargingNetworksListUseCase {
        // For now, only Tesla is supported by PlugAndCharge
        override operator fun invoke(): List<ChargingNetworkType> = listOf(ChargingNetworkType.TESLA)
    }
