/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.landingpage.domain.logic

import com.toyota.oneapp.features.plugandcharge.landingpage.domain.repository.PlugAndChargeToggleRepository
import com.toyota.oneapp.features.plugandcharge.landingpage.domain.usecase.SetPlugAndChargeToggleStatusUseCase
import javax.inject.Inject

class SetPlugAndChargeToggleStatusLogic
    @Inject
    constructor(
        private val plugAndChargeToggleRepository: PlugAndChargeToggleRepository,
    ) : SetPlugAndChargeToggleStatusUseCase {
        override suspend fun invoke(newValue: Boolean): Result<Unit> = plugAndChargeToggleRepository.setToggleStatus(newValue)
    }
