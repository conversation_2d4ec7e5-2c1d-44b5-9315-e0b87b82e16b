/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.landingpage.presentation.composable

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Card
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.toyota.oneapp.R
import com.toyota.oneapp.features.chargingnetwork.domain.model.PlugAndChargeLandingState
import com.toyota.oneapp.features.core.composable.CustomSwitch
import com.toyota.oneapp.features.core.composable.OASubHeadLine1TextView
import com.toyota.oneapp.features.core.composable.spacer.VerticalSpacer
import com.toyota.oneapp.features.core.theme.AppTheme.colors
import com.toyota.oneapp.features.core.theme.ThemeMode
import com.toyota.oneapp.features.core.util.ContentPreview
import com.toyota.oneapp.features.core.util.OAThemePreviewProvider
import com.toyota.oneapp.features.plugandcharge.landingpage.presentation.viewmodel.PlugAndChargeLandingViewModel

@Composable
fun PlugAndChargeSettingsComposable(modifier: Modifier = Modifier) {
    val plugAndChargeLandingViewModel: PlugAndChargeLandingViewModel = hiltViewModel()
    val plugAndChargeState by plugAndChargeLandingViewModel.plugAndChargeState.collectAsState()

    val (isActive, expirationDatetime) =
        when (val state = plugAndChargeState) {
            is PlugAndChargeLandingState.Loaded ->
                Pair(
                    state.isEnabled,
                    stringResource(
                        id = R.string.expires,
                        state.expirationDate,
                    ),
                )
            else -> Pair(false, "")
        }

    Column(
        horizontalAlignment = Alignment.Start,
        modifier = modifier,
    ) {
        OASubHeadLine1TextView(
            text = stringResource(id = R.string.settings),
            color = colors.tertiary03,
        )

        VerticalSpacer(size = 8)

        Card(
            backgroundColor = colors.tile03,
            elevation = 5.dp,
            shape = RoundedCornerShape(8.dp),
        ) {
            Row(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .padding(vertical = 12.dp, horizontal = 12.dp),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    OASubHeadLine1TextView(
                        text = stringResource(id = R.string.plug_and_charge),
                        textAlign = TextAlign.Center,
                        color = colors.tertiary03,
                    )
                    VerticalSpacer(size = 8)
                    PlugAndChargeExpirationDateComposable(
                        isActive = isActive,
                        expirationDatetime = expirationDatetime,
                    )
                }

                CustomSwitch(
                    isEnabled = isActive,
                    // TODO: set testTagId
                    // https://toyotaconnected.atlassian.net/browse/OAD01-28275
                    testTagId = "",
                    onCheckedChange = plugAndChargeLandingViewModel::onPlugAndChargeStatusChange,
                )
            }
        }
    }
}

@Composable
@Preview
private fun PlugAndChargeSettingsComposablePreview(
    @PreviewParameter(OAThemePreviewProvider::class)
    themeMode: ThemeMode,
) {
    ContentPreview(
        modifier = Modifier.wrapContentSize(),
        themeMode = themeMode,
    ) {
        PlugAndChargeSettingsComposable()
    }
}
