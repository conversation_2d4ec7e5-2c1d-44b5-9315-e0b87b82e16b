/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.landingpage.domain.logic

import com.toyota.oneapp.features.chargingnetwork.domain.model.ChargingNetworkType
import com.toyota.oneapp.features.chargingnetwork.domain.model.ChargingNetworksState
import com.toyota.oneapp.features.chargingnetwork.domain.usecase.GetEnabledChargingNetworksUseCase
import com.toyota.oneapp.features.dataconsent.domain.model.DataConsentStrategy
import com.toyota.oneapp.features.plugandcharge.landingpage.domain.usecase.GetPlugAndChargeCompatibleChargingNetworksListUseCase
import com.toyota.oneapp.features.plugandcharge.landingpage.domain.usecase.GetPlugAndChargeCompatibleChargingNetworksUseCase
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject

class GetPlugAndChargeCompatibleChargingNetworksLogic
    @Inject
    constructor(
        private val getEnabledChargingNetworks: GetEnabledChargingNetworksUseCase,
        private val getPlugAndChargeCompatibleChargingNetworksList: GetPlugAndChargeCompatibleChargingNetworksListUseCase,
    ) : GetPlugAndChargeCompatibleChargingNetworksUseCase {
        override fun invoke(): Flow<List<ChargingNetworkType>> =
            flow {
                val compatibleChargingNetworks = getPlugAndChargeCompatibleChargingNetworksList()
                val compatibleNames = compatibleChargingNetworks.map { it.name }.toSet()

                getEnabledChargingNetworks()
                    .filter { chargingNetworkState ->
                        chargingNetworkState.getDataConsentStrategies().any { it.name in compatibleNames }
                    }.map { chargingNetworkState ->
                        chargingNetworkState.getDataConsentStrategies().mapNotNull {
                            if (it.name !in compatibleNames) return@mapNotNull null
                            safeValueOf<ChargingNetworkType>(it.name)
                        }
                    }.filterNotNull()
                    .collect { chargingNetwork ->
                        emit(chargingNetwork)
                    }
            }

        private fun ChargingNetworksState.getDataConsentStrategies(): List<DataConsentStrategy> =
            (this as? ChargingNetworksState.Loaded)?.withDataConsent?.dataConsentStrategies ?: emptyList()

        private inline fun <reified T : Enum<T>> safeValueOf(name: String?): T? = enumValues<T>().find { it.name == name }
    }
