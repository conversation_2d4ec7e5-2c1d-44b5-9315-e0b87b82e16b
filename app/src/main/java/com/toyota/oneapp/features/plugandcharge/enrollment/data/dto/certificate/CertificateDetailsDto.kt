/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.enrollment.data.dto.certificate

import com.google.gson.annotations.SerializedName
import java.util.Date

data class CertificateDetailsDto(
    @SerializedName("type") val type: CertificateTypeDto?,
    @SerializedName("active") val isActive: Boolean?,
    @SerializedName("progress") val progress: Int?,
    @SerializedName("end_date") val endDate: Date?,
)
