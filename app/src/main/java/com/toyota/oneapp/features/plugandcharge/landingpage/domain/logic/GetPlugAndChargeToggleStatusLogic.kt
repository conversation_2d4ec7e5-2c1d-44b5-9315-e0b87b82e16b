/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.landingpage.domain.logic

import com.toyota.oneapp.features.plugandcharge.landingpage.domain.model.PlugAndChargeToggleStatus
import com.toyota.oneapp.features.plugandcharge.landingpage.domain.repository.PlugAndChargeToggleRepository
import com.toyota.oneapp.features.plugandcharge.landingpage.domain.usecase.GetPlugAndChargeToggleStatusUseCase
import kotlinx.coroutines.flow.StateFlow
import javax.inject.Inject

class GetPlugAndChargeToggleStatusLogic
    @Inject
    constructor(
        private val plugAndChargeToggleRepository: PlugAndChargeToggleRepository,
    ) : GetPlugAndChargeToggleStatusUseCase {
        override suspend operator fun invoke(): StateFlow<Result<PlugAndChargeToggleStatus>> {
            plugAndChargeToggleRepository.updateToggleStatus()
            return plugAndChargeToggleRepository.toggleStatus
        }
    }
