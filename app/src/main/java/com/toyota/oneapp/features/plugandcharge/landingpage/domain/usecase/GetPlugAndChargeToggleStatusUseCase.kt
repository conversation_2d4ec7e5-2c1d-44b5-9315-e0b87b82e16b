/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.landingpage.domain.usecase

import com.toyota.oneapp.features.plugandcharge.landingpage.domain.model.PlugAndChargeToggleStatus
import kotlinx.coroutines.flow.StateFlow

fun interface GetPlugAndChargeToggleStatusUseCase {
    suspend operator fun invoke(): StateFlow<Result<PlugAndChargeToggleStatus>>
}
