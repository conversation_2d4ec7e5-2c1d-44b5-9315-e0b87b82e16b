/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.landingpage.data.repository

import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.features.plugandcharge.enrollment.data.api.PlugAndChargeEnrollmentApi
import com.toyota.oneapp.features.plugandcharge.enrollment.data.mapper.PlugAndChargeEnrollmentStatusMapper
import com.toyota.oneapp.features.plugandcharge.enrollment.domain.model.PlugAndChargeEnrollmentStatus
import com.toyota.oneapp.features.plugandcharge.landingpage.data.api.PlugAndChargeToggleApi
import com.toyota.oneapp.features.plugandcharge.landingpage.data.dto.PlugAndChargeToggleRequestDto
import com.toyota.oneapp.features.plugandcharge.landingpage.data.dto.ToggleStatusRequestDto
import com.toyota.oneapp.features.plugandcharge.landingpage.domain.model.PlugAndChargeToggleStatus
import com.toyota.oneapp.features.plugandcharge.landingpage.domain.repository.PlugAndChargeToggleRepository
import com.toyota.oneapp.network.BaseRepository
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.NetworkStatus
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import kotlin.coroutines.CoroutineContext

class PlugAndChargeToggleRepositoryImpl
    @Inject
    constructor(
        errorParser: ErrorMessageParser,
        ioContext: CoroutineContext,
        private val enrollmentApi: PlugAndChargeEnrollmentApi,
        private val toggleApi: PlugAndChargeToggleApi,
        private val statusMapper: PlugAndChargeEnrollmentStatusMapper,
        private val applicationData: ApplicationData,
    ) : BaseRepository(
            errorParser = errorParser,
            ioContext = ioContext,
        ),
        PlugAndChargeToggleRepository {
        private val _toggleStatus: MutableStateFlow<Result<PlugAndChargeToggleStatus>> =
            MutableStateFlow(Result.success(PlugAndChargeToggleStatus(isToggleOn = false, expirationDate = null)))
        override val toggleStatus: StateFlow<Result<PlugAndChargeToggleStatus>> = _toggleStatus.asStateFlow()

        override suspend fun setToggleStatus(value: Boolean): Result<Unit> {
            val vehicle = applicationData.getSelectedVehicle() ?: return getGenericFailure()
            val vin = vehicle.vin ?: return getGenericFailure()
            val region = vehicle.region ?: return getGenericFailure()

            val newValue = if (value) ToggleStatusRequestDto.TOGGLE_ON else ToggleStatusRequestDto.TOGGLE_OFF

            val status =
                makeApiCall {
                    toggleApi.setPlugAndChargeToggleStatus(
                        vin = vin,
                        region = region,
                        brand = vehicle.brand,
                        make = vehicle.brand,
                        body =
                            PlugAndChargeToggleRequestDto(toggle = newValue.value),
                    )
                }.status

            updateToggleStatus()

            return if (status == NetworkStatus.SUCCESS) {
                Result.success(Unit)
            } else {
                getGenericFailure()
            }
        }

        override suspend fun updateToggleStatus() {
            val vin = applicationData.getSelectedVehicle()?.vin
            _toggleStatus.value =
                if (vin == null) {
                    getGenericFailure()
                } else {
                    makeApiCall {
                        enrollmentApi.getPlugAndChargeEnrollmentStatus(vin)
                    }.data?.payload?.let { dto ->
                        val (isToggleOn, expirationDate) =
                            when (val status = statusMapper.map(dto)) {
                                is PlugAndChargeEnrollmentStatus.Enrolled ->
                                    Pair(
                                        status.isToggleOn,
                                        status.expirationDate,
                                    )
                                else -> Pair(false, null)
                            }

                        Result.success(
                            PlugAndChargeToggleStatus(
                                isToggleOn = isToggleOn,
                                expirationDate = expirationDate,
                            ),
                        )
                    } ?: getGenericFailure()
                }
        }

        private fun <T> getGenericFailure(): Result<T> = Result.failure(Exception())
    }
