/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.landingpage.presentation.composable

import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.OABody1TextView
import com.toyota.oneapp.features.core.theme.AppTheme.colors
import com.toyota.oneapp.features.core.theme.ThemeMode
import com.toyota.oneapp.features.core.util.ContentPreview
import com.toyota.oneapp.features.core.util.OAThemePreviewProvider

@Composable
fun PlugAndChargeExpirationDateComposable(
    modifier: Modifier = Modifier,
    isActive: Boolean,
    expirationDatetime: String,
) {
    Row(modifier = modifier) {
        OABody1TextView(
            text = if (isActive) stringResource(R.string.active) else stringResource(R.string.inactive),
            textAlign = TextAlign.Center,
            color = if (isActive) colors.secondary01 else colors.tertiary05,
        )
        OABody1TextView(
            text = expirationDatetime,
            textAlign = TextAlign.Center,
            color = colors.tertiary05,
        )
    }
}

@Composable
@Preview
private fun PlugAndChargeExpirationDateComposablePreview(
    @PreviewParameter(OAThemePreviewProvider::class)
    themeMode: ThemeMode,
) {
    ContentPreview(
        modifier = Modifier.wrapContentSize(),
        themeMode = themeMode,
    ) {
        PlugAndChargeExpirationDateComposable(
            isActive = true,
            expirationDatetime = "2025-06-22",
        )
    }
}
