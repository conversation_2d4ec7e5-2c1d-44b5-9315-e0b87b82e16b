/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.landingpage.presentation.composable

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.toyota.oneapp.R
import com.toyota.oneapp.features.chargingnetwork.domain.model.ChargingNetworkType
import com.toyota.oneapp.features.core.composable.OASubHeadLine1TextView
import com.toyota.oneapp.features.core.composable.spacer.VerticalSpacer
import com.toyota.oneapp.features.core.theme.AppTheme.colors
import com.toyota.oneapp.features.core.theme.ThemeMode
import com.toyota.oneapp.features.core.util.ContentPreview
import com.toyota.oneapp.features.core.util.OAThemePreviewProvider
import com.toyota.oneapp.features.plugandcharge.landingpage.presentation.viewmodel.PlugAndChargeLandingViewModel

@Composable
fun PlugAndChargeCompatibleStationsComposable(modifier: Modifier = Modifier) {
    val plugAndChargeLandingViewModel: PlugAndChargeLandingViewModel = hiltViewModel()
    val compatibleStations by plugAndChargeLandingViewModel.compatibleStationsState.collectAsState()

    val stations = getCompatibleStationData(compatibleStations)

    Column(
        modifier =
            modifier
                .fillMaxWidth(),
        horizontalAlignment = Alignment.Start,
    ) {
        OASubHeadLine1TextView(
            text = stringResource(id = R.string.compatible_stations),
            color = colors.tertiary03,
        )

        VerticalSpacer(size = 8)

        LazyRow(
            modifier = modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp),
        ) {
            items(stations.size) {
                val (drawableId, contentDescriptionId) = stations[it]
                PlugAndChargeCompatibleStationCardComposable(drawableId, contentDescriptionId)
            }
        }
    }
}

private fun getCompatibleStationData(stations: List<ChargingNetworkType>): List<Pair<Int, Int>> =
    stations.mapNotNull {
        when (it) {
            ChargingNetworkType.TESLA -> Pair(R.drawable.tesla, R.string.tesla)
            else -> null
        }
    }

@Composable
@Preview
private fun PlugAndChargeCompatibleStationsComposablePreview(
    @PreviewParameter(OAThemePreviewProvider::class)
    themeMode: ThemeMode,
) {
    ContentPreview(
        themeMode = themeMode,
        modifier = Modifier.wrapContentSize(),
    ) {
        PlugAndChargeCompatibleStationsComposable()
    }
}
