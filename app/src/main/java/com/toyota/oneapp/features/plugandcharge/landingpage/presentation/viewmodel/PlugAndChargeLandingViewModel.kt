/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.landingpage.presentation.viewmodel

import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.features.chargingnetwork.domain.model.ChargingNetworkType
import com.toyota.oneapp.features.chargingnetwork.domain.model.PlugAndChargeLandingState
import com.toyota.oneapp.features.plugandcharge.landingpage.domain.model.PlugAndChargeToggleStatus
import com.toyota.oneapp.features.plugandcharge.landingpage.domain.usecase.GetPlugAndChargeCompatibleChargingNetworksUseCase
import com.toyota.oneapp.features.plugandcharge.landingpage.domain.usecase.GetPlugAndChargeToggleStatusUseCase
import com.toyota.oneapp.features.plugandcharge.landingpage.domain.usecase.SetPlugAndChargeToggleStatusUseCase
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.util.DateUtil
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class PlugAndChargeLandingViewModel
    @Inject
    constructor(
        private val getPlugAndChargeToggleStatus: GetPlugAndChargeToggleStatusUseCase,
        private val setPlugAndChargeToggleStatus: SetPlugAndChargeToggleStatusUseCase,
        private val getPlugAndChargeCompatibleStations: GetPlugAndChargeCompatibleChargingNetworksUseCase,
        private val dateUtil: DateUtil,
    ) : BaseViewModel() {
        private val _plugAndChargeState = MutableStateFlow<PlugAndChargeLandingState>(value = PlugAndChargeLandingState.Loading)
        val plugAndChargeState = _plugAndChargeState.asStateFlow()

        private val _compatibleStationsState = MutableStateFlow<List<ChargingNetworkType>>(value = emptyList())
        val compatibleStationsState = _compatibleStationsState.asStateFlow()

        init {
            observePlugAndChargeActiveState()
            observeCompatibleStations()
        }

        private fun observeCompatibleStations() {
            viewModelScope.launch {
                getPlugAndChargeCompatibleStations().collect { newState ->
                    _compatibleStationsState.value = newState
                }
            }
        }

        private fun observePlugAndChargeActiveState() {
            viewModelScope.launch {
                getPlugAndChargeToggleStatus().collect { result ->
                    when (val status = result.getOrNull()) {
                        is PlugAndChargeToggleStatus ->
                            _plugAndChargeState.value =
                                PlugAndChargeLandingState.Loaded(
                                    isEnabled = status.isToggleOn,
                                    expirationDate =
                                        status.expirationDate?.let {
                                            dateUtil.formatMediumDate(it)
                                        } ?: "",
                                )
                        else ->
                            _plugAndChargeState.value =
                                PlugAndChargeLandingState.Loaded(
                                    isEnabled = false,
                                    expirationDate = "",
                                )
                    }
                }
            }
        }

        fun onPlugAndChargeStatusChange(checked: Boolean) {
            viewModelScope.launch {
                setPlugAndChargeToggleStatus(checked)
            }
        }
    }
