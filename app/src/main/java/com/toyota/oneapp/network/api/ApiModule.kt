package com.toyota.oneapp.network.api

import com.toyota.oneapp.digitalkey.api.SmsOptInServiceApi
import com.toyota.oneapp.features.accountnotification.dataaccess.service.AccountNotificationApi
import com.toyota.oneapp.features.announcementcenter.announcementdetail.dataaccess.service.AnnouncementDetailAPI
import com.toyota.oneapp.features.chargeassist.dataaccess.service.ChargeAssistApi
import com.toyota.oneapp.features.chargehistory.dataaccess.service.ChargeHistoryAPI
import com.toyota.oneapp.features.chargeinfo.dataaccess.service.ChargeInfoAPI
import com.toyota.oneapp.features.chargeschedule.dataaccess.service.ChargeScheduleAPI
import com.toyota.oneapp.features.cleanassist.dataaccess.service.CleanAssistAPI
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.service.CommonApi
import com.toyota.oneapp.features.dashboard.announcement.dataaccess.service.AnnouncementApi
import com.toyota.oneapp.features.dashboard.announcement.dataaccess.service.EvSwapBalanceApi
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.dataaccess.service.RemoteCommandsApi
import com.toyota.oneapp.features.dashboard.connectedvehicle.climate.schedule.dataaccess.service.ClimateScheduleApi
import com.toyota.oneapp.features.dashboard.connectedvehicle.climate.settings.dataaccess.service.ClimateSettingsApi
import com.toyota.oneapp.features.dashboard.connectedvehicle.connectedvehicle.dataaccess.service.CVApi
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.dataaccess.service.Dk7RestServiceApi
import com.toyota.oneapp.features.dashboard.connectedvehicle.remotetab.remotestates.dataaccess.service.RemoteStatesApi
import com.toyota.oneapp.features.dashboard.dashboard.dataaccess.service.DashboardApi
import com.toyota.oneapp.features.dashboard.dashboard.dataaccess.service.TelemetryApi
import com.toyota.oneapp.features.dashboard.noncv.dataaccess.service.NonCVApi
import com.toyota.oneapp.features.dataconsent.dataaccess.service.DataConsentAPI
import com.toyota.oneapp.features.dealerservice.dataaccess.repository.service.DealerServiceApi
import com.toyota.oneapp.features.entrollment.dataaccess.service.EnrollmentApi
import com.toyota.oneapp.features.find.dataacess.service.FindApi
import com.toyota.oneapp.features.findstations.dataaccess.service.FindStationsAPI
import com.toyota.oneapp.features.glovebox.dashboardlights.dataaccess.service.DashboardLightsAPI
import com.toyota.oneapp.features.glovebox.gloveboxlist.dataaccess.service.GloveBoxApi
import com.toyota.oneapp.features.glovebox.manualandwarranties.dataacess.service.ManualAndWarrantiesAPI
import com.toyota.oneapp.features.glovebox.specsandcapabilities.dataaccess.service.SpecsAndCapabilitiesAPI
import com.toyota.oneapp.features.guestdriver.dataaccess.service.GuestDriverAPI
import com.toyota.oneapp.features.pay.tfs.dataaccess.service.TFSApi
import com.toyota.oneapp.features.pay.tfs.dataaccess.service.TFSAuthenticateApi
import com.toyota.oneapp.features.pay.tfs.dataaccess.service.TFSAuthorizeApi
import com.toyota.oneapp.features.pay.tfs.dataaccess.service.TFSEligibilityApi
import com.toyota.oneapp.features.pay.wallet.dataaccess.service.WalletApi
import com.toyota.oneapp.features.plugandcharge.enrollment.data.api.PlugAndChargeEnrollmentApi
import com.toyota.oneapp.features.plugandcharge.landingpage.data.api.PlugAndChargeToggleApi
import com.toyota.oneapp.features.publiccharging.dataaccess.service.PublicChargingApi
import com.toyota.oneapp.features.rentals.dataaccess.service.RentalApi
import com.toyota.oneapp.features.shop.dataaccess.service.ShopApi
import com.toyota.oneapp.features.trips.dataaccess.service.TripsApi
import com.toyota.oneapp.features.vehiclehealth.vehiclehealthlist.dataaccess.service.VehicleHealthAPI
import com.toyota.oneapp.features.vehiclenickname.dataacess.service.VehicleNicknameAPI
import com.toyota.oneapp.features.vehiclesoftware.dataaccess.service.VehicleSoftwareApi
import com.toyota.oneapp.features.vehiclesoftwareupdate21mm.dataaccess.service.SoftwareUpdate21mmService
import com.toyota.oneapp.features.vehiclestatus.dataaccess.service.VehicleStatusApi
import com.toyota.oneapp.features.vehicleswitcher.dataaccess.service.VehicleSwitcherApi
import com.toyota.oneapp.network.api.SXM.SXMServiceAPI
import com.toyota.oneapp.network.api.cy17.CY17ServiceAPI
import com.toyota.oneapp.network.api.cy17plus.CY17PlusCoroutineServiceApi
import com.toyota.oneapp.network.api.cy17plus.CY17PlusInsecureServiceApi
import com.toyota.oneapp.network.api.cy17plus.CY17PlusServiceAPI
import com.toyota.oneapp.network.api.fridp.IDPServiceApi
import com.toyota.oneapp.network.api.mm21.CoroutineService21mmApi
import com.toyota.oneapp.network.api.mm21.DirectionServicesApi
import com.toyota.oneapp.network.api.mm21.LocationServicesApi
import com.toyota.oneapp.network.api.mm21.MusicServicesApi
import com.toyota.oneapp.network.api.mm21.PinServiceApi
import com.toyota.oneapp.network.api.mm21.ProfilePictureServicesApi
import com.toyota.oneapp.network.api.mm21.VaNotificationPreferenceApi
import com.toyota.oneapp.network.api.mm21.VehicleRegistrationPendingApi
import com.toyota.oneapp.network.api.mm21.VehicleSoftwareServiceApi
import com.toyota.oneapp.network.api.ss.ServiceShopServiceAPI
import com.toyota.oneapp.network.api.vgi.VGIServiceAPI
import com.toyota.oneapp.network.token.TokenServiceAPI
import com.toyota.oneapp.ui.ubi.UBIOffersAPIService
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import retrofit2.Retrofit
import javax.inject.Named
import javax.inject.Singleton

@InstallIn(SingletonComponent::class)
@Module
class ApiModule {
    @Provides
    fun provideCy17PlusInsecureServiceApi(
        @Named("insecure") retrofit: Retrofit,
    ): CY17PlusInsecureServiceApi = retrofit.create(CY17PlusInsecureServiceApi::class.java)

    @Provides
    fun provideCy17PlusServiceApi(retrofit: Retrofit): CY17PlusServiceAPI = retrofit.create(CY17PlusServiceAPI::class.java)

    @Provides
    fun provideVgiServiceApi(retrofit: Retrofit): VGIServiceAPI = retrofit.create(VGIServiceAPI::class.java)

    @Provides
    fun provideCy17ServiceApi(retrofit: Retrofit): CY17ServiceAPI = retrofit.create(CY17ServiceAPI::class.java)

    @Provides
    fun provideSxmClient(
        @Named("sxm") retrofit: Retrofit,
    ): SXMServiceAPI = retrofit.create(SXMServiceAPI::class.java)

    @Provides
    fun provideIDPClient(
        @Named("idp") retrofit: Retrofit,
    ): IDPAPI = retrofit.create(IDPAPI::class.java)

    @Provides
    fun provideUBIUserOptStatusApi(retrofit: Retrofit): UBIOffersAPIService = retrofit.create(UBIOffersAPIService::class.java)

    @Provides
    fun provideCY17PlusCoroutineServiceApi(retrofit: Retrofit): CY17PlusCoroutineServiceApi =
        retrofit.create(CY17PlusCoroutineServiceApi::class.java)

    @Provides
    fun provideHealthReportCoroutineServiceAPI(retrofit: Retrofit): HealthReportCoroutineServiceApi =
        retrofit.create(HealthReportCoroutineServiceApi::class.java)

    @Provides
    fun provideIRFCoroutineServiceAPI(retrofit: Retrofit): IRFCoroutineServiceAPI = retrofit.create(IRFCoroutineServiceAPI::class.java)

    // 21mm API should be added below

    @Provides
    fun provideVaNotificationPrefClient(retrofit: Retrofit): VaNotificationPreferenceApi =
        retrofit.create(VaNotificationPreferenceApi::class.java)

    @Provides
    fun provideMusicClient(retrofit: Retrofit): MusicServicesApi = retrofit.create(MusicServicesApi::class.java)

    @Provides
    fun provideVrpsClient(retrofit: Retrofit): VehicleRegistrationPendingApi = retrofit.create(VehicleRegistrationPendingApi::class.java)

    @Provides
    fun providePoiClient(retrofit: Retrofit): LocationServicesApi = retrofit.create(LocationServicesApi::class.java)

    @Provides
    fun provideProfilePictureClient(retrofit: Retrofit): ProfilePictureServicesApi = retrofit.create(ProfilePictureServicesApi::class.java)

    @Provides
    fun provideGoogleClient(
        @Named("google") retrofit: Retrofit,
    ): DirectionServicesApi = retrofit.create(DirectionServicesApi::class.java)

    @Provides
    fun provideTokenServiceApi(
        @Named("idpBase") retrofit: Retrofit,
    ): TokenServiceAPI = retrofit.create(TokenServiceAPI::class.java)

    @Provides
    fun provide21MMCoroutineServiceAPI(retrofit: Retrofit): CoroutineService21mmApi = retrofit.create(CoroutineService21mmApi::class.java)

    @Provides
    fun providePinClient(
        @Named("pin") retrofit: Retrofit,
    ): PinServiceApi = retrofit.create(PinServiceApi::class.java)

    @Provides
    fun provideDk7ServiceAPI(retrofit: Retrofit): Dk7RestServiceApi = retrofit.create(Dk7RestServiceApi::class.java)

    @Provides
    fun provideSmsOptinServiceApi(
        @Named("sms-opt") retrofit: Retrofit,
    ): SmsOptInServiceApi = retrofit.create(SmsOptInServiceApi::class.java)

    @Provides
    fun provideVehicleSoftwareServiceApi(retrofit: Retrofit): VehicleSoftwareServiceApi =
        retrofit.create(VehicleSoftwareServiceApi::class.java)

    @Provides
    fun provideFRIdpServiceApi(
        @Named("fr-idp") retrofit: Retrofit,
    ): IDPServiceApi = retrofit.create(IDPServiceApi::class.java)

    @Provides
    fun provideServiceShopServiceApi(retrofit: Retrofit): ServiceShopServiceAPI = retrofit.create(ServiceShopServiceAPI::class.java)

    @Provides
    fun provideGloveBoxApi(retrofit: Retrofit): GloveBoxApi = retrofit.create(GloveBoxApi::class.java)

    @Provides
    fun provideSpecsAndCapabilitiesApi(retrofit: Retrofit): SpecsAndCapabilitiesAPI = retrofit.create(SpecsAndCapabilitiesAPI::class.java)

    @Provides
    fun provideManualAndWarrantiesApi(retrofit: Retrofit): ManualAndWarrantiesAPI = retrofit.create(ManualAndWarrantiesAPI::class.java)

    @Provides
    fun provideDashboardLightsAPI(retrofit: Retrofit): DashboardLightsAPI = retrofit.create(DashboardLightsAPI::class.java)

    @Provides
    fun provideTopNavApi(retrofit: Retrofit): TelemetryApi = retrofit.create(TelemetryApi::class.java)

    @Provides
    fun provideDashboardApi(retrofit: Retrofit): DashboardApi = retrofit.create(DashboardApi::class.java)

    @Provides
    fun provideRemoteCommandsApi(retrofit: Retrofit): RemoteCommandsApi = retrofit.create(RemoteCommandsApi::class.java)

    @Provides
    fun provideVehicleSwitcherApiApi(retrofit: Retrofit): VehicleSwitcherApi = retrofit.create(VehicleSwitcherApi::class.java)

    @Provides
    fun provideVehicleNicknameApi(retrofit: Retrofit): VehicleNicknameAPI = retrofit.create(VehicleNicknameAPI::class.java)

    @Provides
    fun provideAccountNotificationApi(retrofit: Retrofit): AccountNotificationApi = retrofit.create(AccountNotificationApi::class.java)

    @Provides
    fun provideAnnouncementsApi(retrofit: Retrofit): AnnouncementDetailAPI = retrofit.create(AnnouncementDetailAPI::class.java)

    @Provides
    fun provideShopApi(retrofit: Retrofit): ShopApi = retrofit.create(ShopApi::class.java)

    @Provides
    fun provideRentalApi(retrofit: Retrofit): RentalApi = retrofit.create(RentalApi::class.java)

    @Provides
    fun provideGuestDriverApi(retrofit: Retrofit): GuestDriverAPI = retrofit.create(GuestDriverAPI::class.java)

    @Provides
    fun provideVehicleStatusApi(retrofit: Retrofit): VehicleStatusApi = retrofit.create(VehicleStatusApi::class.java)

    @Provides
    fun provideFindApi(retrofit: Retrofit): FindApi = retrofit.create(FindApi::class.java)

    @Provides
    fun provideChargeAssistApi(retrofit: Retrofit): ChargeAssistApi = retrofit.create(ChargeAssistApi::class.java)

    @Provides
    fun provideFindStationsApi(retrofit: Retrofit): FindStationsAPI = retrofit.create(FindStationsAPI::class.java)

    @Provides
    fun provideTripsApi(retrofit: Retrofit): TripsApi = retrofit.create(TripsApi::class.java)

    @Provides
    fun provideWalletApi(retrofit: Retrofit): WalletApi = retrofit.create(WalletApi::class.java)

    @Provides
    fun provideRemoteStatesApi(retrofit: Retrofit): RemoteStatesApi = retrofit.create(RemoteStatesApi::class.java)

    @Provides
    fun provideCommonApi(retrofit: Retrofit): CommonApi = retrofit.create(CommonApi::class.java)

    @Provides
    fun provideTFSEligibilityApi(retrofit: Retrofit): TFSEligibilityApi = retrofit.create(TFSEligibilityApi::class.java)

    @Provides
    fun provideTFSAuthenticationApi(
        @Named("tfs-authenticate") retrofit: Retrofit,
    ): TFSAuthenticateApi = retrofit.create(TFSAuthenticateApi::class.java)

    @Provides
    fun provideTFSAuthorizeApi(
        @Named("tfs-authorize") retrofit: Retrofit,
    ): TFSAuthorizeApi = retrofit.create(TFSAuthorizeApi::class.java)

    @Provides
    fun provideTFSApi(
        @Named("tfs") retrofit: Retrofit,
    ): TFSApi = retrofit.create(TFSApi::class.java)

    @Provides
    fun provideNonCVApi(retrofit: Retrofit): NonCVApi = retrofit.create(NonCVApi::class.java)

    @Provides
    fun provideVehicleHealthApi(retrofit: Retrofit): VehicleHealthAPI = retrofit.create(VehicleHealthAPI::class.java)

    @Provides
    fun provideAnnouncementApi(retrofit: Retrofit): AnnouncementApi = retrofit.create(AnnouncementApi::class.java)

    @Provides
    fun provideEvSwapBalanceApi(retrofit: Retrofit): EvSwapBalanceApi = retrofit.create(EvSwapBalanceApi::class.java)

    @Provides
    fun provideCVApi(retrofit: Retrofit): CVApi = retrofit.create(CVApi::class.java)

    @Provides
    fun provideSoftwareUpdateApis(retrofit: Retrofit): VehicleSoftwareApi = retrofit.create(VehicleSoftwareApi::class.java)

    @Provides
    fun provideClimateSettingsApi(retrofit: Retrofit): ClimateSettingsApi = retrofit.create(ClimateSettingsApi::class.java)

    @Provides
    fun provideClimateScheduleApi(retrofit: Retrofit): ClimateScheduleApi = retrofit.create(ClimateScheduleApi::class.java)

    @Provides
    fun provideSoftwareUpdate21mmService(retrofit: Retrofit): SoftwareUpdate21mmService =
        retrofit.create(SoftwareUpdate21mmService::class.java)

    @Provides
    fun provideChargeScheduleApi(retrofit: Retrofit): ChargeScheduleAPI = retrofit.create(ChargeScheduleAPI::class.java)

    @Provides
    fun provideDealerServiceApi(retrofit: Retrofit): DealerServiceApi = retrofit.create(DealerServiceApi::class.java)

    @Provides
    fun provideChargeInfoAPI(retrofit: Retrofit): ChargeInfoAPI = retrofit.create(ChargeInfoAPI::class.java)

    @Provides
    fun provideChargeHistoryAPI(retrofit: Retrofit): ChargeHistoryAPI = retrofit.create(ChargeHistoryAPI::class.java)

    @Provides
    fun provideCleanAssistAPI(retrofit: Retrofit): CleanAssistAPI = retrofit.create(CleanAssistAPI::class.java)

    @Provides
    fun provideEnrollmentApi(retrofit: Retrofit): EnrollmentApi = retrofit.create(EnrollmentApi::class.java)

    @Provides
    @Singleton
    fun providePublicChargingAPI(retrofit: Retrofit): PublicChargingApi = retrofit.create(PublicChargingApi::class.java)

    @Provides
    fun provideDataConsentAPI(retrofit: Retrofit): DataConsentAPI = retrofit.create(DataConsentAPI::class.java)

    @Provides
    fun providePlugAndChargeEnrollmentApi(retrofit: Retrofit): PlugAndChargeEnrollmentApi =
        retrofit.create(PlugAndChargeEnrollmentApi::class.java)

    @Provides
    fun providePlugAndChargeToggleApi(retrofit: Retrofit): PlugAndChargeToggleApi = retrofit.create(PlugAndChargeToggleApi::class.java)
}
